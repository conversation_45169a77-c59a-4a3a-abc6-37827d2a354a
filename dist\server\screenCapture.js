import sharp from 'sharp';
import { CaptureError } from '../types/index.js';
import { logger } from '../utils/logger.js';
// Dynamic imports for optional dependencies
let screenshotDesktop;
let robotjs;
// Try to load optional screen capture libraries
try {
    screenshotDesktop = await import('screenshot-desktop');
}
catch (error) {
    logger.warn('screenshot-desktop not available, some features may be limited');
}
try {
    robotjs = await import('robotjs');
}
catch (error) {
    logger.warn('robotjs not available, some features may be limited');
}
/**
 * Cross-platform screen capture implementation with multiple backend support
 */
export class ScreenCapture {
    availableBackends = [];
    constructor() {
        this.detectAvailableBackends();
    }
    /**
     * Detect available screen capture backends
     */
    detectAvailableBackends() {
        if (screenshotDesktop) {
            this.availableBackends.push('screenshot-desktop');
        }
        if (robotjs) {
            this.availableBackends.push('robotjs');
        }
        // Fallback: Node.js native approach (limited functionality)
        this.availableBackends.push('native');
        logger.info('Available screen capture backends:', this.availableBackends);
    }
    /**
     * Capture screen with specified options
     */
    async capture(options = {}) {
        const { monitor = 0, region, quality = 80, format = 'jpeg' } = options;
        logger.debug('Capturing screen', { monitor, region, quality, format });
        try {
            let rawBuffer;
            // Try different backends in order of preference
            if (this.availableBackends.includes('screenshot-desktop')) {
                const result = await this.captureWithScreenshotDesktop(monitor, region);
                rawBuffer = result.buffer;
            }
            else if (this.availableBackends.includes('robotjs')) {
                const result = await this.captureWithRobotJS(region);
                rawBuffer = result.buffer;
            }
            else {
                throw new CaptureError('No screen capture backend available. Please install screenshot-desktop or robotjs.', 'NO_BACKEND_AVAILABLE');
            }
            // Process image with Sharp
            const processedResult = await this.processImage(rawBuffer, {
                quality,
                format,
                region
            });
            return {
                buffer: processedResult.buffer,
                base64: processedResult.buffer.toString('base64'),
                width: processedResult.width,
                height: processedResult.height,
                format: processedResult.format,
                compressionRatio: rawBuffer.length / processedResult.buffer.length
            };
        }
        catch (error) {
            logger.error('Screen capture failed', error);
            throw new CaptureError(`Screen capture failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'CAPTURE_FAILED', monitor);
        }
    }
    /**
     * Capture using screenshot-desktop library
     */
    async captureWithScreenshotDesktop(monitor, region) {
        if (!screenshotDesktop) {
            throw new Error('screenshot-desktop not available');
        }
        const displays = await screenshotDesktop.listDisplays();
        if (monitor >= displays.length) {
            throw new CaptureError(`Monitor ${monitor} not found. Available monitors: 0-${displays.length - 1}`, 'MONITOR_NOT_FOUND', monitor);
        }
        const display = displays[monitor];
        const options = { screen: display.id };
        // Add region if specified
        if (region) {
            options.x = region.x;
            options.y = region.y;
            options.width = region.width;
            options.height = region.height;
        }
        const buffer = await screenshotDesktop.screenshot(options);
        // Get image metadata
        const image = sharp(buffer);
        const { width, height } = await image.metadata();
        return {
            buffer,
            metadata: { width: width || 0, height: height || 0 }
        };
    }
    /**
     * Capture using robotjs library
     */
    async captureWithRobotJS(region) {
        if (!robotjs) {
            throw new Error('robotjs not available');
        }
        const screenSize = robotjs.getScreenSize();
        const captureRegion = region || {
            x: 0,
            y: 0,
            width: screenSize.width,
            height: screenSize.height
        };
        const bitmap = robotjs.screen.capture(captureRegion.x, captureRegion.y, captureRegion.width, captureRegion.height);
        // Convert bitmap to buffer
        const buffer = this.bitmapToBuffer(bitmap);
        return {
            buffer,
            metadata: {
                width: captureRegion.width,
                height: captureRegion.height
            }
        };
    }
    /**
     * Convert robotjs bitmap to buffer
     */
    bitmapToBuffer(bitmap) {
        const { width, height, image } = bitmap;
        const buffer = Buffer.alloc(width * height * 4);
        // Convert BGRA to RGBA
        for (let i = 0; i < image.length; i += 4) {
            buffer[i] = image[i + 2]; // R
            buffer[i + 1] = image[i + 1]; // G
            buffer[i + 2] = image[i]; // B
            buffer[i + 3] = image[i + 3]; // A
        }
        return buffer;
    }
    /**
     * Process captured image with Sharp
     */
    async processImage(buffer, options) {
        let image = sharp(buffer);
        // Apply region cropping if needed (for backends that don't support it natively)
        if (options.region) {
            image = image.extract({
                left: options.region.x,
                top: options.region.y,
                width: options.region.width,
                height: options.region.height
            });
        }
        // Apply format and quality
        switch (options.format) {
            case 'jpeg':
                image = image.jpeg({ quality: options.quality });
                break;
            case 'png':
                image = image.png({ compressionLevel: Math.floor((100 - options.quality) / 10) });
                break;
            case 'webp':
                image = image.webp({ quality: options.quality });
                break;
            default:
                image = image.jpeg({ quality: options.quality });
        }
        const processedBuffer = await image.toBuffer();
        const metadata = await sharp(processedBuffer).metadata();
        return {
            buffer: processedBuffer,
            width: metadata.width || 0,
            height: metadata.height || 0,
            format: options.format
        };
    }
    /**
     * Get available monitors
     */
    async getMonitors() {
        try {
            if (screenshotDesktop) {
                const displays = await screenshotDesktop.listDisplays();
                return displays.map((display, index) => ({
                    id: index,
                    name: display.name || `Monitor ${index + 1}`,
                    primary: display.primary || index === 0,
                    bounds: {
                        x: display.left || 0,
                        y: display.top || 0,
                        width: display.right - display.left || display.width || 1920,
                        height: display.bottom - display.top || display.height || 1080
                    },
                    scaleFactor: display.scaleFactor || 1
                }));
            }
            else if (robotjs) {
                const screenSize = robotjs.getScreenSize();
                return [{
                        id: 0,
                        name: 'Primary Monitor',
                        primary: true,
                        bounds: {
                            x: 0,
                            y: 0,
                            width: screenSize.width,
                            height: screenSize.height
                        },
                        scaleFactor: 1
                    }];
            }
            else {
                // Fallback: assume standard monitor
                return [{
                        id: 0,
                        name: 'Default Monitor',
                        primary: true,
                        bounds: {
                            x: 0,
                            y: 0,
                            width: 1920,
                            height: 1080
                        },
                        scaleFactor: 1
                    }];
            }
        }
        catch (error) {
            logger.error('Failed to get monitor information', error);
            throw new CaptureError('Failed to retrieve monitor information', 'MONITOR_INFO_FAILED');
        }
    }
    /**
     * Test screen capture functionality
     */
    async testCapture() {
        try {
            await this.capture({ quality: 50, format: 'jpeg' });
            return true;
        }
        catch (error) {
            logger.error('Screen capture test failed', error);
            return false;
        }
    }
    /**
     * Get capture capabilities
     */
    getCapabilities() {
        const features = ['basic_capture'];
        if (this.availableBackends.includes('screenshot-desktop')) {
            features.push('multi_monitor', 'display_info');
        }
        if (this.availableBackends.includes('robotjs')) {
            features.push('cursor_position', 'screen_size');
        }
        return {
            backends: this.availableBackends,
            formats: ['jpeg', 'png', 'webp'],
            features
        };
    }
}
//# sourceMappingURL=screenCapture.js.map