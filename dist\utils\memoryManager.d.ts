/**
 * Memory management utilities to prevent memory leaks and optimize performance
 */
export declare class MemoryManager {
    private memoryCheckInterval;
    private memoryLimitBytes;
    private gcThreshold;
    private lastGCTime;
    private memoryHistory;
    constructor();
    /**
     * Start memory monitoring
     */
    private startMemoryMonitoring;
    /**
     * Stop memory monitoring
     */
    stopMemoryMonitoring(): void;
    /**
     * Check current memory usage and trigger GC if needed
     */
    private checkMemoryUsage;
    /**
     * Determine if GC should be triggered based on memory trends
     */
    private shouldTriggerGC;
    /**
     * Force garbage collection
     */
    private forceGarbageCollection;
    /**
     * Emit memory warning event
     */
    private emitMemoryWarning;
    /**
     * Get current memory statistics
     */
    getMemoryStats(): {
        current: NodeJS.MemoryUsage;
        limit: number;
        usage: {
            percentage: number;
            trend: 'increasing' | 'decreasing' | 'stable';
        };
        gc: {
            lastGCTime: number;
            timeSinceLastGC: number;
        };
    };
    /**
     * Clean up resources
     */
    cleanup(): void;
    /**
     * Check if memory usage is healthy
     */
    isMemoryHealthy(): boolean;
    /**
     * Get memory recommendations
     */
    getMemoryRecommendations(): string[];
}
export declare const memoryManager: MemoryManager;
//# sourceMappingURL=memoryManager.d.ts.map