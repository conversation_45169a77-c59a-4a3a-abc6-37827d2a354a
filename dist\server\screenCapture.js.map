{"version": 3, "file": "screenCapture.js", "sourceRoot": "", "sources": ["../../src/server/screenCapture.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAe,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,4CAA4C;AAC5C,IAAI,iBAAsB,CAAC;AAC3B,IAAI,OAAY,CAAC;AAEjB,gDAAgD;AAChD,IAAI,CAAC;IACH,iBAAiB,GAAG,MAAM,MAAM,CAAC,oBAA2B,CAAC,CAAC;AAChE,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IACf,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;AAChF,CAAC;AAED,IAAI,CAAC;IACH,OAAO,GAAG,MAAM,MAAM,CAAC,SAAgB,CAAC,CAAC;AAC3C,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IACf,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;AACrE,CAAC;AAuBD;;GAEG;AACH,MAAM,OAAO,aAAa;IAChB,iBAAiB,GAAa,EAAE,CAAC;IAEzC;QACE,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,IAAI,iBAAiB,EAAE,CAAC;YACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QAED,4DAA4D;QAC5D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEtC,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,UAA0B,EAAE;QACxC,MAAM,EACJ,OAAO,GAAG,CAAC,EACX,MAAM,EACN,OAAO,GAAG,EAAE,EACZ,MAAM,GAAG,MAAM,EAChB,GAAG,OAAO,CAAC;QAEZ,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,IAAI,SAAiB,CAAC;YAEtB,gDAAgD;YAChD,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBAC1D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACxE,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;YAC5B,CAAC;iBAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBACrD,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,YAAY,CACpB,oFAAoF,EACpF,sBAAsB,CACvB,CAAC;YACJ,CAAC;YAED,2BAA2B;YAC3B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE;gBACzD,OAAO;gBACP,MAAM;gBACN,MAAM;aACP,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACjD,KAAK,EAAE,eAAe,CAAC,KAAK;gBAC5B,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,gBAAgB,EAAE,SAAS,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM;aACnE,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,IAAI,YAAY,CACpB,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EACpF,gBAAgB,EAChB,OAAO,CACR,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,4BAA4B,CACxC,OAAe,EACf,MAAgE;QAEhE,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,YAAY,EAAE,CAAC;QAExD,IAAI,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,IAAI,YAAY,CACpB,WAAW,OAAO,qCAAqC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,EAC5E,mBAAmB,EACnB,OAAO,CACR,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClC,MAAM,OAAO,GAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QAE5C,0BAA0B;QAC1B,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;YACrB,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;YACrB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YAC7B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QACjC,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE3D,qBAAqB;QACrB,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAC5B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;QAEjD,OAAO;YACL,MAAM;YACN,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,EAAE;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,MAAgE;QAEhE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,MAAM,aAAa,GAAG,MAAM,IAAI;YAC9B,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC;QAEF,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CACnC,aAAa,CAAC,CAAC,EACf,aAAa,CAAC,CAAC,EACf,aAAa,CAAC,KAAK,EACnB,aAAa,CAAC,MAAM,CACrB,CAAC;QAEF,2BAA2B;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE3C,OAAO;YACL,MAAM;YACN,QAAQ,EAAE;gBACR,KAAK,EAAE,aAAa,CAAC,KAAK;gBAC1B,MAAM,EAAE,aAAa,CAAC,MAAM;aAC7B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,MAAW;QAChC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QACxC,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QAEhD,uBAAuB;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAK,IAAI;YAClC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;YAClC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAK,IAAI;YAClC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QACpC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CACxB,MAAc,EACd,OAIC;QAED,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAE1B,gFAAgF;QAChF,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC;gBACpB,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBACtB,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrB,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK;gBAC3B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;YACvB,KAAK,MAAM;gBACT,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gBACjD,MAAM;YACR,KAAK,KAAK;gBACR,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBAClF,MAAM;YACR,KAAK,MAAM;gBACT,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gBACjD,MAAM;YACR;gBACE,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC;QAEzD,OAAO;YACL,MAAM,EAAE,eAAe;YACvB,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,CAAC;YAC1B,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,IAAI,iBAAiB,EAAE,CAAC;gBACtB,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,YAAY,EAAE,CAAC;gBACxD,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAY,EAAE,KAAa,EAAE,EAAE,CAAC,CAAC;oBACpD,EAAE,EAAE,KAAK;oBACT,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,WAAW,KAAK,GAAG,CAAC,EAAE;oBAC5C,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK,KAAK,CAAC;oBACvC,MAAM,EAAE;wBACN,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC;wBACpB,CAAC,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;wBACnB,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI;wBAC5D,MAAM,EAAE,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI;qBAC/D;oBACD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;iBACtC,CAAC,CAAC,CAAC;YACN,CAAC;iBAAM,IAAI,OAAO,EAAE,CAAC;gBACnB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC3C,OAAO,CAAC;wBACN,EAAE,EAAE,CAAC;wBACL,IAAI,EAAE,iBAAiB;wBACvB,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE;4BACN,CAAC,EAAE,CAAC;4BACJ,CAAC,EAAE,CAAC;4BACJ,KAAK,EAAE,UAAU,CAAC,KAAK;4BACvB,MAAM,EAAE,UAAU,CAAC,MAAM;yBAC1B;wBACD,WAAW,EAAE,CAAC;qBACf,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,oCAAoC;gBACpC,OAAO,CAAC;wBACN,EAAE,EAAE,CAAC;wBACL,IAAI,EAAE,iBAAiB;wBACvB,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE;4BACN,CAAC,EAAE,CAAC;4BACJ,CAAC,EAAE,CAAC;4BACJ,KAAK,EAAE,IAAI;4BACX,MAAM,EAAE,IAAI;yBACb;wBACD,WAAW,EAAE,CAAC;qBACf,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,YAAY,CACpB,wCAAwC,EACxC,qBAAqB,CACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QAKb,MAAM,QAAQ,GAAa,CAAC,eAAe,CAAC,CAAC;QAE7C,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAC1D,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/C,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;QAClD,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,iBAAiB;YAChC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;YAChC,QAAQ;SACT,CAAC;IACJ,CAAC;CACF"}