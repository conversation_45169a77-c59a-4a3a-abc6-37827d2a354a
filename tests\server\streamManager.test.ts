import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { StreamManager } from '../../src/server/streamManager.js';
import { StreamError } from '../../src/types/index.js';

// Mock ScreenCapture
vi.mock('../../src/server/screenCapture.js', () => ({
  ScreenCapture: vi.fn().mockImplementation(() => ({
    capture: vi.fn().mockResolvedValue({
      buffer: Buffer.from('mock-image-data'),
      base64: 'bW9jay1pbWFnZS1kYXRh',
      width: 1920,
      height: 1080,
      format: 'jpeg',
      compressionRatio: 2.5
    })
  }))
}));

describe('StreamManager', () => {
  let streamManager: StreamManager;

  beforeEach(() => {
    streamManager = new StreamManager();
  });

  afterEach(async () => {
    await streamManager.stopAllStreams();
  });

  describe('startStream', () => {
    it('should start a new stream with default configuration', async () => {
      const streamId = await streamManager.startStream({});
      
      expect(streamId).toBeDefined();
      expect(typeof streamId).toBe('string');
      expect(streamManager.hasStream(streamId)).toBe(true);
      expect(streamManager.getActiveStreamCount()).toBe(1);
    });

    it('should start a stream with custom configuration', async () => {
      const config = {
        fps: 2,
        quality: 90,
        monitor: 0,
        enableDiffDetection: false,
        format: 'png' as const
      };

      const streamId = await streamManager.startStream(config);
      const status = streamManager.getStreamStatus(streamId);
      
      expect(Array.isArray(status)).toBe(false);
      if (!Array.isArray(status)) {
        expect(status.config.fps).toBe(2);
        expect(status.config.quality).toBe(90);
        expect(status.config.format).toBe('png');
        expect(status.config.enableDiffDetection).toBe(false);
      }
    });

    it('should reject invalid configuration', async () => {
      const invalidConfig = {
        fps: -1, // Invalid FPS
        quality: 150 // Invalid quality
      };

      await expect(streamManager.startStream(invalidConfig)).rejects.toThrow();
    });

    it('should enforce maximum concurrent streams limit', async () => {
      // Start maximum allowed streams (5 by default in test config)
      const streamIds: string[] = [];
      for (let i = 0; i < 5; i++) {
        const streamId = await streamManager.startStream({});
        streamIds.push(streamId);
      }

      // Try to start one more stream
      await expect(streamManager.startStream({})).rejects.toThrow(StreamError);
      
      // Cleanup
      for (const streamId of streamIds) {
        await streamManager.stopStream(streamId);
      }
    });
  });

  describe('stopStream', () => {
    it('should stop an existing stream', async () => {
      const streamId = await streamManager.startStream({});
      
      const result = await streamManager.stopStream(streamId);
      
      expect(result).toBe(true);
      expect(streamManager.hasStream(streamId)).toBe(false);
      expect(streamManager.getActiveStreamCount()).toBe(0);
    });

    it('should throw error when stopping non-existent stream', async () => {
      await expect(streamManager.stopStream('non-existent-id')).rejects.toThrow(StreamError);
    });
  });

  describe('configureStream', () => {
    it('should update stream configuration', async () => {
      const streamId = await streamManager.startStream({ fps: 1, quality: 80 });
      
      const result = await streamManager.configureStream(streamId, {
        fps: 3,
        quality: 95
      });
      
      expect(result).toBe(true);
      
      const status = streamManager.getStreamStatus(streamId);
      if (!Array.isArray(status)) {
        expect(status.config.fps).toBe(3);
        expect(status.config.quality).toBe(95);
      }
    });

    it('should throw error when configuring non-existent stream', async () => {
      await expect(streamManager.configureStream('non-existent-id', { fps: 2 }))
        .rejects.toThrow(StreamError);
    });
  });

  describe('getStreamStatus', () => {
    it('should return status for specific stream', async () => {
      const streamId = await streamManager.startStream({ fps: 2 });
      
      const status = streamManager.getStreamStatus(streamId);
      
      expect(Array.isArray(status)).toBe(false);
      if (!Array.isArray(status)) {
        expect(status.streamId).toBe(streamId);
        expect(status.isActive).toBe(true);
        expect(status.config.fps).toBe(2);
        expect(status.stats).toBeDefined();
      }
    });

    it('should return status for all streams', async () => {
      const streamId1 = await streamManager.startStream({ fps: 1 });
      const streamId2 = await streamManager.startStream({ fps: 2 });
      
      const statuses = streamManager.getStreamStatus();
      
      expect(Array.isArray(statuses)).toBe(true);
      if (Array.isArray(statuses)) {
        expect(statuses).toHaveLength(2);
        expect(statuses.map(s => s.streamId)).toContain(streamId1);
        expect(statuses.map(s => s.streamId)).toContain(streamId2);
      }
    });

    it('should throw error for non-existent stream', () => {
      expect(() => streamManager.getStreamStatus('non-existent-id')).toThrow(StreamError);
    });
  });

  describe('stopAllStreams', () => {
    it('should stop all active streams', async () => {
      const streamId1 = await streamManager.startStream({});
      const streamId2 = await streamManager.startStream({});
      
      expect(streamManager.getActiveStreamCount()).toBe(2);
      
      await streamManager.stopAllStreams();
      
      expect(streamManager.getActiveStreamCount()).toBe(0);
      expect(streamManager.hasStream(streamId1)).toBe(false);
      expect(streamManager.hasStream(streamId2)).toBe(false);
    });
  });

  describe('stream events', () => {
    it('should emit streamData events', async () => {
      const streamId = await streamManager.startStream({ fps: 10 }); // High FPS for faster testing
      
      const streamDataPromise = new Promise((resolve) => {
        streamManager.once('streamData', resolve);
      });

      // Wait for at least one frame
      const streamData = await Promise.race([
        streamDataPromise,
        new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 2000))
      ]);

      expect(streamData).toBeDefined();
      
      await streamManager.stopStream(streamId);
    });

    it('should emit streamStopped events', async () => {
      const streamId = await streamManager.startStream({});
      
      const streamStoppedPromise = new Promise((resolve) => {
        streamManager.once('streamStopped', resolve);
      });

      await streamManager.stopStream(streamId);
      
      const stoppedStreamId = await streamStoppedPromise;
      expect(stoppedStreamId).toBe(streamId);
    });
  });
});
