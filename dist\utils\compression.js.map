{"version": 3, "file": "compression.js", "sourceRoot": "", "sources": ["../../src/utils/compression.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAkBrC;;GAEG;AACH,MAAM,OAAO,eAAe;IAC1B;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CACnB,WAAmB,EACnB,OAA2B;QAE3B,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC;QAExC,IAAI,CAAC;YACH,IAAI,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;YAE/B,oCAAoC;YACpC,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,MAAM;oBACT,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;wBACjB,OAAO,EAAE,OAAO,CAAC,OAAO;wBACxB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI;wBACxC,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,IAAI;wBAC5C,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI;qBACjC,CAAC,CAAC;oBACH,MAAM;gBAER,KAAK,KAAK;oBACR,yDAAyD;oBACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;oBAClE,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;wBAChB,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;wBAC5D,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,KAAK;wBACzC,OAAO,EAAE,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC,gCAAgC;qBAC/D,CAAC,CAAC;oBACH,MAAM;gBAER,KAAK,MAAM;oBACT,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;wBACjB,OAAO,EAAE,OAAO,CAAC,OAAO;wBACxB,QAAQ,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;wBAC/B,YAAY,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE,IAAI,OAAO,CAAC,OAAO,GAAG,EAAE;wBAC3D,cAAc,EAAE,IAAI;qBACrB,CAAC,CAAC;oBACH,MAAM;gBAER;oBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,gBAAgB,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;YAChD,MAAM,cAAc,GAAG,gBAAgB,CAAC,MAAM,CAAC;YAC/C,MAAM,gBAAgB,GAAG,YAAY,GAAG,cAAc,CAAC;YAEvD,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBAC/B,YAAY;gBACZ,cAAc;gBACd,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC1D,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,gBAAgB;gBACxB,YAAY;gBACZ,cAAc;gBACd,gBAAgB;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACrG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CACvB,WAAmB,EACnB,YAAqB,EACrB,aAAqB,EAAE;QAEvB,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;QACjC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;QAExC,uDAAuD;QACvD,IAAI,MAAM,GAA4B,MAAM,CAAC;QAE7C,IAAI,QAAQ,CAAC,QAAQ,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACjD,uCAAuC;YACvC,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;aAAM,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YACtD,qDAAqD;YACrD,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;QAED,+CAA+C;QAC/C,IAAI,OAAO,GAAG,UAAU,CAAC;QACzB,IAAI,MAAyB,CAAC;QAE9B,GAAG,CAAC;YACF,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAE/D,gEAAgE;YAChE,IAAI,YAAY,IAAI,MAAM,CAAC,cAAc,GAAG,YAAY,GAAG,IAAI,EAAE,CAAC;gBAChE,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,MAAM;YACR,CAAC;QACH,CAAC,QAAQ,OAAO,GAAG,EAAE,EAAE;QAEvB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAC3B,WAAmB,EACnB,WAAwD,EACxD,UAAkB,EAAE;QAEpB,IAAI,OAA2B,CAAC;QAEhC,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,YAAY;gBACf,8CAA8C;gBAC9C,OAAO,GAAG;oBACR,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,uBAAuB;oBACvD,MAAM,EAAE,KAAK,EAAE,yBAAyB;oBACxC,WAAW,EAAE,KAAK;iBACnB,CAAC;gBACF,MAAM;YAER,KAAK,OAAO;gBACV,uCAAuC;gBACvC,OAAO,GAAG;oBACR,OAAO;oBACP,MAAM,EAAE,MAAM;oBACd,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,IAAI;iBACd,CAAC;gBACF,MAAM;YAER,KAAK,SAAS;gBACZ,6CAA6C;gBAC7C,OAAO,GAAG;oBACR,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;oBAC9B,MAAM,EAAE,KAAK;oBACb,WAAW,EAAE,KAAK;iBACnB,CAAC;gBACF,MAAM;YAER,KAAK,MAAM;gBACT,0CAA0C;gBAC1C,OAAO,GAAG;oBACR,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;oBAC9B,MAAM,EAAE,KAAK;oBACb,WAAW,EAAE,KAAK;iBACnB,CAAC;gBACF,MAAM;YAER;gBACE,OAAO,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QAC1C,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,WAAmB,EACnB,QAAgB,EAChB,SAAiB,EACjB,OAA2B;QAE3B,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;QACjC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;QAExC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,oDAAoD;QACpD,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;QACrD,IAAI,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC9B,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEhC,IAAI,QAAQ,GAAG,QAAQ,EAAE,CAAC;YACxB,QAAQ,GAAG,QAAQ,CAAC;YACpB,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,SAAS,GAAG,SAAS,EAAE,CAAC;YAC1B,SAAS,GAAG,SAAS,CAAC;YACtB,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;QACjD,CAAC;QAED,mBAAmB;QACnB,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,QAAQ,KAAK,QAAQ,CAAC,KAAK,IAAI,SAAS,KAAK,QAAQ,CAAC,MAAM,EAAE,CAAC;YACjE,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,EAAE;gBACjD,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ;gBAC7B,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,QAAQ,EAAE,CAAC;QACtD,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF"}