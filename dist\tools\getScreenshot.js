import { ScreenCapture } from '../server/screenCapture.js';
import { GetScreenshotInputSchema } from '../types/index.js';
import { logger } from '../utils/logger.js';
/**
 * Get single screenshot tool handler
 */
export async function getScreenshotTool(args) {
    try {
        // Validate input arguments
        const validatedArgs = GetScreenshotInputSchema.parse(args);
        logger.info('Capturing single screenshot', validatedArgs);
        const screenCapture = new ScreenCapture();
        // Capture screenshot
        const result = await screenCapture.capture({
            monitor: validatedArgs.monitor,
            region: validatedArgs.region,
            quality: validatedArgs.quality,
            format: validatedArgs.format
        });
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        screenshot: result.base64,
                        metadata: {
                            width: result.width,
                            height: result.height,
                            format: result.format,
                            size: result.buffer.length,
                            compressionRatio: result.compressionRatio,
                            timestamp: Date.now()
                        },
                        captureOptions: validatedArgs
                    }, null, 2)
                }
            ]
        };
    }
    catch (error) {
        logger.error('Failed to capture screenshot', error);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                        code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
                    }, null, 2)
                }
            ],
            isError: true
        };
    }
}
//# sourceMappingURL=getScreenshot.js.map