import { RateLimiterMemory, RateLimiterRes } from 'rate-limiter-flexible';
import { logger } from './logger.js';
import { serverConfig } from './config.js';

/**
 * Rate limiting utilities for security and performance
 */
export class RateLimiter {
  private toolCallLimiter: RateLimiterMemory;
  private streamStartLimiter: RateLimiterMemory;
  private screenshotLimiter: RateLimiterMemory;

  constructor() {
    // General tool call rate limiter
    this.toolCallLimiter = new RateLimiterMemory({
      points: serverConfig.security.rateLimitPerMinute,
      duration: 60, // Per 60 seconds
      blockDuration: 60, // Block for 60 seconds if limit exceeded
    });

    // Stream start rate limiter (more restrictive)
    this.streamStartLimiter = new RateLimiterMemory({
      points: 10, // Max 10 stream starts per minute
      duration: 60,
      blockDuration: 300, // Block for 5 minutes
    });

    // Screenshot rate limiter
    this.screenshotLimiter = new RateLimiterMemory({
      points: 30, // Max 30 screenshots per minute
      duration: 60,
      blockDuration: 60,
    });
  }

  /**
   * Check rate limit for tool calls
   */
  async checkToolCallLimit(): Promise<void> {
    try {
      await this.toolCallLimiter.consume('global');
    } catch (rejRes) {
      const secs = Math.round((rejRes as RateLimiterRes).msBeforeNext / 1000) || 1;
      logger.warn(`Tool call rate limit exceeded. Try again in ${secs} seconds`);
      throw new Error(`Rate limit exceeded. Try again in ${secs} seconds`);
    }
  }

  /**
   * Check rate limit for stream starts
   */
  async checkStreamStartLimit(): Promise<void> {
    try {
      await this.streamStartLimiter.consume('stream_start');
    } catch (rejRes) {
      const secs = Math.round((rejRes as RateLimiterRes).msBeforeNext / 1000) || 1;
      logger.warn(`Stream start rate limit exceeded. Try again in ${secs} seconds`);
      throw new Error(`Too many stream starts. Try again in ${secs} seconds`);
    }
  }

  /**
   * Check rate limit for screenshots
   */
  async checkScreenshotLimit(): Promise<void> {
    try {
      await this.screenshotLimiter.consume('screenshot');
    } catch (rejRes) {
      const secs = Math.round((rejRes as RateLimiterRes).msBeforeNext / 1000) || 1;
      logger.warn(`Screenshot rate limit exceeded. Try again in ${secs} seconds`);
      throw new Error(`Too many screenshot requests. Try again in ${secs} seconds`);
    }
  }

  /**
   * Get current rate limit status
   */
  async getRateLimitStatus(): Promise<{
    toolCalls: { remaining: number; resetTime: Date };
    streamStarts: { remaining: number; resetTime: Date };
    screenshots: { remaining: number; resetTime: Date };
  }> {
    const [toolCallsRes, streamStartsRes, screenshotsRes] = await Promise.all([
      this.toolCallLimiter.get('global'),
      this.streamStartLimiter.get('stream_start'),
      this.screenshotLimiter.get('screenshot')
    ]);

    return {
      toolCalls: {
        remaining: toolCallsRes ? this.toolCallLimiter.points - (toolCallsRes as any).hitCount : this.toolCallLimiter.points,
        resetTime: new Date(Date.now() + (toolCallsRes?.msBeforeNext || 0))
      },
      streamStarts: {
        remaining: streamStartsRes ? this.streamStartLimiter.points - (streamStartsRes as any).hitCount : this.streamStartLimiter.points,
        resetTime: new Date(Date.now() + (streamStartsRes?.msBeforeNext || 0))
      },
      screenshots: {
        remaining: screenshotsRes ? this.screenshotLimiter.points - (screenshotsRes as any).hitCount : this.screenshotLimiter.points,
        resetTime: new Date(Date.now() + (screenshotsRes?.msBeforeNext || 0))
      }
    };
  }

  /**
   * Reset rate limits (for testing or admin purposes)
   */
  async resetLimits(): Promise<void> {
    await Promise.all([
      this.toolCallLimiter.delete('global'),
      this.streamStartLimiter.delete('stream_start'),
      this.screenshotLimiter.delete('screenshot')
    ]);
    
    logger.info('Rate limits reset');
  }
}

// Global rate limiter instance
export const rateLimiter = new RateLimiter();
