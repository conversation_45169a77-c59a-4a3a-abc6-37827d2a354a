import { z } from 'zod';
export declare const StreamConfigSchema: z.ZodObject<{
    fps: z.ZodD<PERSON><PERSON><z.ZodNumber>;
    quality: z.<PERSON><PERSON><PERSON><z.ZodNumber>;
    monitor: z.<PERSON><PERSON><PERSON><z.ZodNumber>;
    region: z.Zod<PERSON>ptional<z.ZodObject<{
        x: z.ZodNumber;
        y: z.ZodNumber;
        width: z.ZodNumber;
        height: z.ZodNumber;
    }, "strip", z.ZodType<PERSON>ny, {
        x: number;
        y: number;
        width: number;
        height: number;
    }, {
        x: number;
        y: number;
        width: number;
        height: number;
    }>>;
    enableDiffDetection: z.<PERSON>efault<z.ZodBoolean>;
    enableCompression: z.Z<PERSON>Default<z.ZodBoolean>;
    format: z.ZodDefault<z.ZodEnum<["jpeg", "png", "webp"]>>;
}, "strip", z.ZodType<PERSON>ny, {
    fps: number;
    quality: number;
    monitor: number;
    enableDiffDetection: boolean;
    enableCompression: boolean;
    format: "jpeg" | "png" | "webp";
    region?: {
        x: number;
        y: number;
        width: number;
        height: number;
    } | undefined;
}, {
    fps?: number | undefined;
    quality?: number | undefined;
    monitor?: number | undefined;
    region?: {
        x: number;
        y: number;
        width: number;
        height: number;
    } | undefined;
    enableDiffDetection?: boolean | undefined;
    enableCompression?: boolean | undefined;
    format?: "jpeg" | "png" | "webp" | undefined;
}>;
export type StreamConfig = z.infer<typeof StreamConfigSchema>;
export declare const StreamDataSchema: z.ZodObject<{
    streamId: z.ZodString;
    timestamp: z.ZodNumber;
    frameNumber: z.ZodNumber;
    screenshot: z.ZodString;
    metadata: z.ZodObject<{
        width: z.ZodNumber;
        height: z.ZodNumber;
        format: z.ZodString;
        size: z.ZodNumber;
        compressionRatio: z.ZodOptional<z.ZodNumber>;
        captureTime: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        width: number;
        height: number;
        format: string;
        size: number;
        captureTime: number;
        compressionRatio?: number | undefined;
    }, {
        width: number;
        height: number;
        format: string;
        size: number;
        captureTime: number;
        compressionRatio?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    streamId: string;
    timestamp: number;
    frameNumber: number;
    screenshot: string;
    metadata: {
        width: number;
        height: number;
        format: string;
        size: number;
        captureTime: number;
        compressionRatio?: number | undefined;
    };
}, {
    streamId: string;
    timestamp: number;
    frameNumber: number;
    screenshot: string;
    metadata: {
        width: number;
        height: number;
        format: string;
        size: number;
        captureTime: number;
        compressionRatio?: number | undefined;
    };
}>;
export type StreamData = z.infer<typeof StreamDataSchema>;
export declare const StreamStatusSchema: z.ZodObject<{
    streamId: z.ZodString;
    isActive: z.ZodBoolean;
    config: z.ZodObject<{
        fps: z.ZodDefault<z.ZodNumber>;
        quality: z.ZodDefault<z.ZodNumber>;
        monitor: z.ZodDefault<z.ZodNumber>;
        region: z.ZodOptional<z.ZodObject<{
            x: z.ZodNumber;
            y: z.ZodNumber;
            width: z.ZodNumber;
            height: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            x: number;
            y: number;
            width: number;
            height: number;
        }, {
            x: number;
            y: number;
            width: number;
            height: number;
        }>>;
        enableDiffDetection: z.ZodDefault<z.ZodBoolean>;
        enableCompression: z.ZodDefault<z.ZodBoolean>;
        format: z.ZodDefault<z.ZodEnum<["jpeg", "png", "webp"]>>;
    }, "strip", z.ZodTypeAny, {
        fps: number;
        quality: number;
        monitor: number;
        enableDiffDetection: boolean;
        enableCompression: boolean;
        format: "jpeg" | "png" | "webp";
        region?: {
            x: number;
            y: number;
            width: number;
            height: number;
        } | undefined;
    }, {
        fps?: number | undefined;
        quality?: number | undefined;
        monitor?: number | undefined;
        region?: {
            x: number;
            y: number;
            width: number;
            height: number;
        } | undefined;
        enableDiffDetection?: boolean | undefined;
        enableCompression?: boolean | undefined;
        format?: "jpeg" | "png" | "webp" | undefined;
    }>;
    stats: z.ZodObject<{
        totalFrames: z.ZodNumber;
        droppedFrames: z.ZodNumber;
        averageFps: z.ZodNumber;
        averageCaptureTime: z.ZodNumber;
        totalDataSent: z.ZodNumber;
        uptime: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        totalFrames: number;
        droppedFrames: number;
        averageFps: number;
        averageCaptureTime: number;
        totalDataSent: number;
        uptime: number;
    }, {
        totalFrames: number;
        droppedFrames: number;
        averageFps: number;
        averageCaptureTime: number;
        totalDataSent: number;
        uptime: number;
    }>;
    lastFrame: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    streamId: string;
    isActive: boolean;
    config: {
        fps: number;
        quality: number;
        monitor: number;
        enableDiffDetection: boolean;
        enableCompression: boolean;
        format: "jpeg" | "png" | "webp";
        region?: {
            x: number;
            y: number;
            width: number;
            height: number;
        } | undefined;
    };
    stats: {
        totalFrames: number;
        droppedFrames: number;
        averageFps: number;
        averageCaptureTime: number;
        totalDataSent: number;
        uptime: number;
    };
    lastFrame?: number | undefined;
}, {
    streamId: string;
    isActive: boolean;
    config: {
        fps?: number | undefined;
        quality?: number | undefined;
        monitor?: number | undefined;
        region?: {
            x: number;
            y: number;
            width: number;
            height: number;
        } | undefined;
        enableDiffDetection?: boolean | undefined;
        enableCompression?: boolean | undefined;
        format?: "jpeg" | "png" | "webp" | undefined;
    };
    stats: {
        totalFrames: number;
        droppedFrames: number;
        averageFps: number;
        averageCaptureTime: number;
        totalDataSent: number;
        uptime: number;
    };
    lastFrame?: number | undefined;
}>;
export type StreamStatus = z.infer<typeof StreamStatusSchema>;
export declare const MonitorInfoSchema: z.ZodObject<{
    id: z.ZodNumber;
    name: z.ZodString;
    primary: z.ZodBoolean;
    bounds: z.ZodObject<{
        x: z.ZodNumber;
        y: z.ZodNumber;
        width: z.ZodNumber;
        height: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        x: number;
        y: number;
        width: number;
        height: number;
    }, {
        x: number;
        y: number;
        width: number;
        height: number;
    }>;
    scaleFactor: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    id: number;
    name: string;
    primary: boolean;
    bounds: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    scaleFactor: number;
}, {
    id: number;
    name: string;
    primary: boolean;
    bounds: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    scaleFactor?: number | undefined;
}>;
export type MonitorInfo = z.infer<typeof MonitorInfoSchema>;
export declare class StreamError extends Error {
    code: string;
    streamId?: string | undefined;
    constructor(message: string, code: string, streamId?: string | undefined);
}
export declare class CaptureError extends Error {
    code: string;
    monitor?: number | undefined;
    constructor(message: string, code: string, monitor?: number | undefined);
}
export declare const StartStreamInputSchema: z.ZodObject<{
    fps: z.ZodOptional<z.ZodNumber>;
    quality: z.ZodOptional<z.ZodNumber>;
    monitor: z.ZodOptional<z.ZodNumber>;
    region: z.ZodOptional<z.ZodObject<{
        x: z.ZodNumber;
        y: z.ZodNumber;
        width: z.ZodNumber;
        height: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        x: number;
        y: number;
        width: number;
        height: number;
    }, {
        x: number;
        y: number;
        width: number;
        height: number;
    }>>;
    enableDiffDetection: z.ZodOptional<z.ZodBoolean>;
    format: z.ZodOptional<z.ZodEnum<["jpeg", "png", "webp"]>>;
}, "strip", z.ZodTypeAny, {
    fps?: number | undefined;
    quality?: number | undefined;
    monitor?: number | undefined;
    region?: {
        x: number;
        y: number;
        width: number;
        height: number;
    } | undefined;
    enableDiffDetection?: boolean | undefined;
    format?: "jpeg" | "png" | "webp" | undefined;
}, {
    fps?: number | undefined;
    quality?: number | undefined;
    monitor?: number | undefined;
    region?: {
        x: number;
        y: number;
        width: number;
        height: number;
    } | undefined;
    enableDiffDetection?: boolean | undefined;
    format?: "jpeg" | "png" | "webp" | undefined;
}>;
export declare const StopStreamInputSchema: z.ZodObject<{
    streamId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    streamId: string;
}, {
    streamId: string;
}>;
export declare const ConfigureStreamInputSchema: z.ZodObject<{
    streamId: z.ZodString;
    fps: z.ZodOptional<z.ZodNumber>;
    quality: z.ZodOptional<z.ZodNumber>;
    enableDiffDetection: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    streamId: string;
    fps?: number | undefined;
    quality?: number | undefined;
    enableDiffDetection?: boolean | undefined;
}, {
    streamId: string;
    fps?: number | undefined;
    quality?: number | undefined;
    enableDiffDetection?: boolean | undefined;
}>;
export declare const GetScreenshotInputSchema: z.ZodObject<{
    monitor: z.ZodOptional<z.ZodNumber>;
    region: z.ZodOptional<z.ZodObject<{
        x: z.ZodNumber;
        y: z.ZodNumber;
        width: z.ZodNumber;
        height: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        x: number;
        y: number;
        width: number;
        height: number;
    }, {
        x: number;
        y: number;
        width: number;
        height: number;
    }>>;
    quality: z.ZodOptional<z.ZodNumber>;
    format: z.ZodOptional<z.ZodEnum<["jpeg", "png", "webp"]>>;
}, "strip", z.ZodTypeAny, {
    quality?: number | undefined;
    monitor?: number | undefined;
    region?: {
        x: number;
        y: number;
        width: number;
        height: number;
    } | undefined;
    format?: "jpeg" | "png" | "webp" | undefined;
}, {
    quality?: number | undefined;
    monitor?: number | undefined;
    region?: {
        x: number;
        y: number;
        width: number;
        height: number;
    } | undefined;
    format?: "jpeg" | "png" | "webp" | undefined;
}>;
export declare const ServerConfigSchema: z.ZodObject<{
    port: z.ZodDefault<z.ZodNumber>;
    host: z.ZodDefault<z.ZodString>;
    nodeEnv: z.ZodDefault<z.ZodEnum<["development", "production", "test"]>>;
    streaming: z.ZodObject<{
        defaultFps: z.ZodDefault<z.ZodNumber>;
        maxFps: z.ZodDefault<z.ZodNumber>;
        defaultQuality: z.ZodDefault<z.ZodNumber>;
        maxConcurrentStreams: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        defaultFps: number;
        maxFps: number;
        defaultQuality: number;
        maxConcurrentStreams: number;
    }, {
        defaultFps?: number | undefined;
        maxFps?: number | undefined;
        defaultQuality?: number | undefined;
        maxConcurrentStreams?: number | undefined;
    }>;
    security: z.ZodObject<{
        enableAuth: z.ZodDefault<z.ZodBoolean>;
        rateLimitPerMinute: z.ZodDefault<z.ZodNumber>;
        allowedOrigins: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        enableAuth: boolean;
        rateLimitPerMinute: number;
        allowedOrigins: string[];
    }, {
        enableAuth?: boolean | undefined;
        rateLimitPerMinute?: number | undefined;
        allowedOrigins?: string[] | undefined;
    }>;
    performance: z.ZodObject<{
        enableDiffDetection: z.ZodDefault<z.ZodBoolean>;
        enableHardwareAcceleration: z.ZodDefault<z.ZodBoolean>;
        memoryLimitMb: z.ZodDefault<z.ZodNumber>;
        cacheSizeMb: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        enableDiffDetection: boolean;
        enableHardwareAcceleration: boolean;
        memoryLimitMb: number;
        cacheSizeMb: number;
    }, {
        enableDiffDetection?: boolean | undefined;
        enableHardwareAcceleration?: boolean | undefined;
        memoryLimitMb?: number | undefined;
        cacheSizeMb?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    port: number;
    host: string;
    nodeEnv: "development" | "production" | "test";
    streaming: {
        defaultFps: number;
        maxFps: number;
        defaultQuality: number;
        maxConcurrentStreams: number;
    };
    security: {
        enableAuth: boolean;
        rateLimitPerMinute: number;
        allowedOrigins: string[];
    };
    performance: {
        enableDiffDetection: boolean;
        enableHardwareAcceleration: boolean;
        memoryLimitMb: number;
        cacheSizeMb: number;
    };
}, {
    streaming: {
        defaultFps?: number | undefined;
        maxFps?: number | undefined;
        defaultQuality?: number | undefined;
        maxConcurrentStreams?: number | undefined;
    };
    security: {
        enableAuth?: boolean | undefined;
        rateLimitPerMinute?: number | undefined;
        allowedOrigins?: string[] | undefined;
    };
    performance: {
        enableDiffDetection?: boolean | undefined;
        enableHardwareAcceleration?: boolean | undefined;
        memoryLimitMb?: number | undefined;
        cacheSizeMb?: number | undefined;
    };
    port?: number | undefined;
    host?: string | undefined;
    nodeEnv?: "development" | "production" | "test" | undefined;
}>;
export type ServerConfig = z.infer<typeof ServerConfigSchema>;
//# sourceMappingURL=index.d.ts.map