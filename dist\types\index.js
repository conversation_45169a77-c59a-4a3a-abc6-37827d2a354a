import { z } from 'zod';
// Stream Configuration Schema
export const StreamConfigSchema = z.object({
    fps: z.number().min(0.1).max(10).default(1),
    quality: z.number().min(1).max(100).default(80),
    monitor: z.number().min(0).default(0),
    region: z.object({
        x: z.number().min(0),
        y: z.number().min(0),
        width: z.number().min(1),
        height: z.number().min(1)
    }).optional(),
    enableDiffDetection: z.boolean().default(true),
    enableCompression: z.boolean().default(true),
    format: z.enum(['jpeg', 'png', 'webp']).default('jpeg')
});
// Stream Data Schema
export const StreamDataSchema = z.object({
    streamId: z.string(),
    timestamp: z.number(),
    frameNumber: z.number(),
    screenshot: z.string(), // base64 encoded image
    metadata: z.object({
        width: z.number(),
        height: z.number(),
        format: z.string(),
        size: z.number(), // file size in bytes
        compressionRatio: z.number().optional(),
        captureTime: z.number() // capture duration in ms
    })
});
// Stream Status Schema
export const StreamStatusSchema = z.object({
    streamId: z.string(),
    isActive: z.boolean(),
    config: StreamConfigSchema,
    stats: z.object({
        totalFrames: z.number(),
        droppedFrames: z.number(),
        averageFps: z.number(),
        averageCaptureTime: z.number(),
        totalDataSent: z.number(), // bytes
        uptime: z.number() // seconds
    }),
    lastFrame: z.number().optional()
});
// Monitor Info Schema
export const MonitorInfoSchema = z.object({
    id: z.number(),
    name: z.string(),
    primary: z.boolean(),
    bounds: z.object({
        x: z.number(),
        y: z.number(),
        width: z.number(),
        height: z.number()
    }),
    scaleFactor: z.number().default(1)
});
// Error Types
export class StreamError extends Error {
    code;
    streamId;
    constructor(message, code, streamId) {
        super(message);
        this.code = code;
        this.streamId = streamId;
        this.name = 'StreamError';
    }
}
export class CaptureError extends Error {
    code;
    monitor;
    constructor(message, code, monitor) {
        super(message);
        this.code = code;
        this.monitor = monitor;
        this.name = 'CaptureError';
    }
}
// Tool Input Schemas for MCP
export const StartStreamInputSchema = z.object({
    fps: z.number().min(0.1).max(10).optional(),
    quality: z.number().min(1).max(100).optional(),
    monitor: z.number().min(0).optional(),
    region: z.object({
        x: z.number().min(0),
        y: z.number().min(0),
        width: z.number().min(1),
        height: z.number().min(1)
    }).optional(),
    enableDiffDetection: z.boolean().optional(),
    format: z.enum(['jpeg', 'png', 'webp']).optional()
});
export const StopStreamInputSchema = z.object({
    streamId: z.string()
});
export const ConfigureStreamInputSchema = z.object({
    streamId: z.string(),
    fps: z.number().min(0.1).max(10).optional(),
    quality: z.number().min(1).max(100).optional(),
    enableDiffDetection: z.boolean().optional()
});
export const GetScreenshotInputSchema = z.object({
    monitor: z.number().min(0).optional(),
    region: z.object({
        x: z.number().min(0),
        y: z.number().min(0),
        width: z.number().min(1),
        height: z.number().min(1)
    }).optional(),
    quality: z.number().min(1).max(100).optional(),
    format: z.enum(['jpeg', 'png', 'webp']).optional()
});
// Configuration Schema
export const ServerConfigSchema = z.object({
    port: z.number().min(1).max(65535).default(3000),
    host: z.string().default('localhost'),
    nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
    streaming: z.object({
        defaultFps: z.number().min(0.1).max(10).default(1),
        maxFps: z.number().min(1).max(10).default(10),
        defaultQuality: z.number().min(1).max(100).default(80),
        maxConcurrentStreams: z.number().min(1).max(20).default(5)
    }),
    security: z.object({
        enableAuth: z.boolean().default(true),
        rateLimitPerMinute: z.number().min(1).default(100),
        allowedOrigins: z.array(z.string()).default(['*'])
    }),
    performance: z.object({
        enableDiffDetection: z.boolean().default(true),
        enableHardwareAcceleration: z.boolean().default(false),
        memoryLimitMb: z.number().min(64).default(512),
        cacheSizeMb: z.number().min(10).default(100)
    })
});
//# sourceMappingURL=index.js.map