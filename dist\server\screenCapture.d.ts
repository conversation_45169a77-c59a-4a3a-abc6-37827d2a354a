import { MonitorInfo } from '../types/index.js';
export interface CaptureOptions {
    monitor?: number;
    region?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    quality?: number;
    format?: 'jpeg' | 'png' | 'webp';
}
export interface CaptureResult {
    buffer: Buffer;
    base64: string;
    width: number;
    height: number;
    format: string;
    compressionRatio?: number;
}
/**
 * Cross-platform screen capture implementation with multiple backend support
 */
export declare class ScreenCapture {
    private availableBackends;
    constructor();
    /**
     * Detect available screen capture backends
     */
    private detectAvailableBackends;
    /**
     * Capture screen with specified options
     */
    capture(options?: CaptureOptions): Promise<CaptureResult>;
    /**
     * Capture using screenshot-desktop library
     */
    private captureWithScreenshotDesktop;
    /**
     * Capture using robotjs library
     */
    private captureWithRobotJS;
    /**
     * Convert robotjs bitmap to buffer
     */
    private bitmapToBuffer;
    /**
     * Process captured image with Sharp
     */
    private processImage;
    /**
     * Get available monitors
     */
    getMonitors(): Promise<MonitorInfo[]>;
    /**
     * Test screen capture functionality
     */
    testCapture(): Promise<boolean>;
    /**
     * Get capture capabilities
     */
    getCapabilities(): {
        backends: string[];
        formats: string[];
        features: string[];
    };
}
//# sourceMappingURL=screenCapture.d.ts.map