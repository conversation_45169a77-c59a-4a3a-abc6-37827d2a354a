/**
 * MCP Screen Streaming Server
 * Provides real-time screen capture and streaming capabilities for AI assistants
 */
export declare class MCPScreenStreamingServer {
    private server;
    private streamManager;
    private tools;
    constructor();
    /**
     * Initialize all available tools
     */
    private initializeTools;
    /**
     * Setup MCP request handlers
     */
    private setupHandlers;
    /**
     * Start the MCP server
     */
    start(): Promise<void>;
    /**
     * Stop the server and cleanup resources
     */
    stop(): Promise<void>;
}
//# sourceMappingURL=index.d.ts.map