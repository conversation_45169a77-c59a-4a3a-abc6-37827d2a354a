/**
 * Base64 encoding/decoding utilities with optimization
 */
/**
 * Encode buffer to base64 with optional chunking for large data
 */
export declare function encodeBase64(buffer: Buffer, chunkSize?: number): string;
/**
 * Decode base64 string to buffer
 */
export declare function decodeBase64(base64String: string): Buffer;
/**
 * Get base64 data size in bytes
 */
export declare function getBase64Size(base64String: string): number;
/**
 * Create data URL from buffer and MIME type
 */
export declare function createDataURL(buffer: Buffer, mimeType: string): string;
/**
 * Extract MIME type from data URL
 */
export declare function extractMimeType(dataURL: string): string | null;
/**
 * Validate base64 string
 */
export declare function isValidBase64(str: string): boolean;
/**
 * Compress base64 string by removing unnecessary whitespace and formatting
 */
export declare function compressBase64(base64String: string): string;
/**
 * Split large base64 string into chunks for streaming
 */
export declare function chunkBase64(base64String: string, chunkSize?: number): string[];
//# sourceMappingURL=base64Utils.d.ts.map