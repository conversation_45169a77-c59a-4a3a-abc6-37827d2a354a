import { EventEmitter } from 'events';
import { logger } from './logger.js';
import { memoryManager } from './memoryManager.js';
/**
 * Performance monitoring and metrics collection
 */
export class PerformanceMonitor extends EventEmitter {
    metrics = new Map();
    startTimes = new Map();
    counters = new Map();
    monitoringInterval = null;
    constructor() {
        super();
        this.startMonitoring();
    }
    /**
     * Start performance monitoring
     */
    startMonitoring() {
        this.monitoringInterval = setInterval(() => {
            this.collectSystemMetrics();
        }, 60000); // Collect metrics every minute
        logger.info('Performance monitoring started');
    }
    /**
     * Stop performance monitoring
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
            logger.info('Performance monitoring stopped');
        }
    }
    /**
     * Start timing an operation
     */
    startTimer(operation) {
        this.startTimes.set(operation, Date.now());
    }
    /**
     * End timing an operation and record the duration
     */
    endTimer(operation) {
        const startTime = this.startTimes.get(operation);
        if (!startTime) {
            logger.warn(`Timer not found for operation: ${operation}`);
            return 0;
        }
        const duration = Date.now() - startTime;
        this.recordMetric(operation, duration);
        this.startTimes.delete(operation);
        return duration;
    }
    /**
     * Record a metric value
     */
    recordMetric(name, value) {
        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }
        const values = this.metrics.get(name);
        values.push(value);
        // Keep only last 100 values to prevent memory growth
        if (values.length > 100) {
            values.shift();
        }
        // Emit event for real-time monitoring
        this.emit('metric', { name, value, timestamp: Date.now() });
    }
    /**
     * Increment a counter
     */
    incrementCounter(name, amount = 1) {
        const current = this.counters.get(name) || 0;
        this.counters.set(name, current + amount);
    }
    /**
     * Get counter value
     */
    getCounter(name) {
        return this.counters.get(name) || 0;
    }
    /**
     * Reset a counter
     */
    resetCounter(name) {
        this.counters.set(name, 0);
    }
    /**
     * Get statistics for a metric
     */
    getMetricStats(name) {
        const values = this.metrics.get(name);
        if (!values || values.length === 0) {
            return null;
        }
        const sorted = [...values].sort((a, b) => a - b);
        const count = values.length;
        const min = sorted[0];
        const max = sorted[count - 1];
        const sum = values.reduce((a, b) => a + b, 0);
        const average = sum / count;
        const median = sorted[Math.floor(count / 2)];
        const p95Index = Math.floor(count * 0.95);
        const p95 = sorted[p95Index];
        const latest = values[values.length - 1];
        return {
            count,
            min: Math.round(min * 100) / 100,
            max: Math.round(max * 100) / 100,
            average: Math.round(average * 100) / 100,
            median: Math.round(median * 100) / 100,
            p95: Math.round(p95 * 100) / 100,
            latest: Math.round(latest * 100) / 100
        };
    }
    /**
     * Collect system-level metrics
     */
    collectSystemMetrics() {
        // CPU usage (approximation using process.cpuUsage())
        const cpuUsage = process.cpuUsage();
        const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds
        this.recordMetric('system.cpu_usage', cpuPercent);
        // Memory metrics
        const memStats = memoryManager.getMemoryStats();
        this.recordMetric('system.memory_usage_mb', memStats.current.heapUsed / 1024 / 1024);
        this.recordMetric('system.memory_percentage', memStats.usage.percentage);
        // Event loop lag (simple approximation)
        const start = Date.now();
        setImmediate(() => {
            const lag = Date.now() - start;
            this.recordMetric('system.event_loop_lag', lag);
        });
        logger.debug('System metrics collected', {
            cpuPercent: Math.round(cpuPercent * 100) / 100,
            memoryMB: Math.round(memStats.current.heapUsed / 1024 / 1024),
            memoryPercent: Math.round(memStats.usage.percentage * 100) / 100
        });
    }
    /**
     * Get all performance metrics
     */
    getAllMetrics() {
        const metrics = {};
        for (const [name] of this.metrics) {
            metrics[name] = this.getMetricStats(name);
        }
        const counters = {};
        for (const [name, value] of this.counters) {
            counters[name] = value;
        }
        return {
            metrics,
            counters,
            system: {
                uptime: process.uptime(),
                memory: memoryManager.getMemoryStats(),
                nodeVersion: process.version,
                platform: process.platform
            }
        };
    }
    /**
     * Get performance summary
     */
    getPerformanceSummary() {
        const issues = [];
        const recommendations = [];
        let status = 'healthy';
        // Check response times
        const responseTimeStats = this.getMetricStats('tool_execution_time');
        const avgResponseTime = responseTimeStats?.average || null;
        if (avgResponseTime && avgResponseTime > 5000) {
            issues.push('High average response time');
            status = 'warning';
            recommendations.push('Consider optimizing tool execution or reducing stream quality');
        }
        // Check error rate
        const totalRequests = this.getCounter('total_requests') || 1;
        const totalErrors = this.getCounter('total_errors') || 0;
        const errorRate = (totalErrors / totalRequests) * 100;
        if (errorRate > 10) {
            issues.push('High error rate');
            status = 'critical';
            recommendations.push('Investigate error causes and improve error handling');
        }
        else if (errorRate > 5) {
            issues.push('Elevated error rate');
            status = 'warning';
        }
        // Check memory usage
        const memStats = memoryManager.getMemoryStats();
        if (memStats.usage.percentage > 90) {
            issues.push('Critical memory usage');
            status = 'critical';
            recommendations.push('Stop some streams or restart the server');
        }
        else if (memStats.usage.percentage > 80) {
            issues.push('High memory usage');
            if (status === 'healthy')
                status = 'warning';
        }
        // Add memory recommendations
        recommendations.push(...memoryManager.getMemoryRecommendations());
        return {
            status,
            issues,
            recommendations,
            keyMetrics: {
                averageResponseTime: avgResponseTime,
                errorRate: Math.round(errorRate * 100) / 100,
                memoryUsage: Math.round(memStats.usage.percentage * 100) / 100,
                activeStreams: this.getCounter('active_streams') || 0
            }
        };
    }
    /**
     * Clear all metrics and counters
     */
    clearMetrics() {
        this.metrics.clear();
        this.counters.clear();
        this.startTimes.clear();
        logger.info('Performance metrics cleared');
    }
    /**
     * Cleanup resources
     */
    cleanup() {
        this.stopMonitoring();
        this.clearMetrics();
        this.removeAllListeners();
    }
}
// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();
//# sourceMappingURL=performanceMonitor.js.map