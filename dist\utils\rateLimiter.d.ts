/**
 * Rate limiting utilities for security and performance
 */
export declare class RateLimiter {
    private toolCallLimiter;
    private streamStartLimiter;
    private screenshotLimiter;
    constructor();
    /**
     * Check rate limit for tool calls
     */
    checkToolCallLimit(): Promise<void>;
    /**
     * Check rate limit for stream starts
     */
    checkStreamStartLimit(): Promise<void>;
    /**
     * Check rate limit for screenshots
     */
    checkScreenshotLimit(): Promise<void>;
    /**
     * Get current rate limit status
     */
    getRateLimitStatus(): Promise<{
        toolCalls: {
            remaining: number;
            resetTime: Date;
        };
        streamStarts: {
            remaining: number;
            resetTime: Date;
        };
        screenshots: {
            remaining: number;
            resetTime: Date;
        };
    }>;
    /**
     * Reset rate limits (for testing or admin purposes)
     */
    resetLimits(): Promise<void>;
}
export declare const rateLimiter: RateLimiter;
//# sourceMappingURL=rateLimiter.d.ts.map