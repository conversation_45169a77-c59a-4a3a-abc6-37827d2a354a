#!/usr/bin/env node
import { MCPScreenStreamingServer } from './server/index.js';
import { logger } from './utils/logger.js';
/**
 * Main entry point for MCP Screen Streaming Server
 */
async function main() {
    try {
        logger.info('Initializing MCP Screen Streaming Server...');
        const server = new MCPScreenStreamingServer();
        // Setup graceful shutdown
        const shutdown = async () => {
            logger.info('Shutting down server...');
            await server.stop();
            process.exit(0);
        };
        process.on('SIGINT', shutdown);
        process.on('SIGTERM', shutdown);
        process.on('SIGQUIT', shutdown);
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught exception:', error);
            shutdown().catch(() => process.exit(1));
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled rejection at:', promise, 'reason:', reason);
            shutdown().catch(() => process.exit(1));
        });
        // Start the server
        await server.start();
    }
    catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
}
// Run the server
main().catch((error) => {
    logger.error('Fatal error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map