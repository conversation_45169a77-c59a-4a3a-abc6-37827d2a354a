/**
 * Simple logger utility with different log levels
 */
export var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
})(LogLevel || (LogLevel = {}));
class Logger {
    level;
    constructor() {
        const envLevel = process.env.LOG_LEVEL?.toLowerCase() || 'info';
        this.level = this.parseLogLevel(envLevel);
    }
    parseLogLevel(level) {
        switch (level) {
            case 'error': return LogLevel.ERROR;
            case 'warn': return LogLevel.WARN;
            case 'info': return LogLevel.INFO;
            case 'debug': return LogLevel.DEBUG;
            default: return LogLevel.INFO;
        }
    }
    shouldLog(level) {
        return level <= this.level;
    }
    formatMessage(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const formattedArgs = args.length > 0 ? ' ' + args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ') : '';
        return `[${timestamp}] ${level.toUpperCase()}: ${message}${formattedArgs}`;
    }
    error(message, ...args) {
        if (this.shouldLog(LogLevel.ERROR)) {
            console.error(this.formatMessage('error', message, ...args));
        }
    }
    warn(message, ...args) {
        if (this.shouldLog(LogLevel.WARN)) {
            console.warn(this.formatMessage('warn', message, ...args));
        }
    }
    info(message, ...args) {
        if (this.shouldLog(LogLevel.INFO)) {
            console.info(this.formatMessage('info', message, ...args));
        }
    }
    debug(message, ...args) {
        if (this.shouldLog(LogLevel.DEBUG)) {
            console.debug(this.formatMessage('debug', message, ...args));
        }
    }
    // Audit logging for security-sensitive operations
    audit(operation, details) {
        if (process.env.ENABLE_AUDIT_LOG === 'true') {
            const auditEntry = {
                timestamp: new Date().toISOString(),
                operation,
                details,
                pid: process.pid
            };
            console.info(`AUDIT: ${JSON.stringify(auditEntry)}`);
        }
    }
}
export const logger = new Logger();
//# sourceMappingURL=logger.js.map