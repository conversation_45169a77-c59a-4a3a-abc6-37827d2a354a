import { ServerConfig } from '../types/index.js';
/**
 * Load and validate server configuration from environment variables
 */
export declare function loadConfig(): ServerConfig;
/**
 * Get current configuration
 */
export declare const serverConfig: {
    port: number;
    host: string;
    nodeEnv: "development" | "production" | "test";
    streaming: {
        defaultFps: number;
        maxFps: number;
        defaultQuality: number;
        maxConcurrentStreams: number;
    };
    security: {
        enableAuth: boolean;
        rateLimitPerMinute: number;
        allowedOrigins: string[];
    };
    performance: {
        enableDiffDetection: boolean;
        enableHardwareAcceleration: boolean;
        memoryLimitMb: number;
        cacheSizeMb: number;
    };
};
//# sourceMappingURL=config.d.ts.map