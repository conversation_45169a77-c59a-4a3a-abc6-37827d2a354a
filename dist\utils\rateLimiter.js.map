{"version": 3, "file": "rateLimiter.js", "sourceRoot": "", "sources": ["../../src/utils/rateLimiter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAkB,MAAM,uBAAuB,CAAC;AAC1E,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAE3C;;GAEG;AACH,MAAM,OAAO,WAAW;IACd,eAAe,CAAoB;IACnC,kBAAkB,CAAoB;IACtC,iBAAiB,CAAoB;IAE7C;QACE,iCAAiC;QACjC,IAAI,CAAC,eAAe,GAAG,IAAI,iBAAiB,CAAC;YAC3C,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,kBAAkB;YAChD,QAAQ,EAAE,EAAE,EAAE,iBAAiB;YAC/B,aAAa,EAAE,EAAE,EAAE,yCAAyC;SAC7D,CAAC,CAAC;QAEH,+CAA+C;QAC/C,IAAI,CAAC,kBAAkB,GAAG,IAAI,iBAAiB,CAAC;YAC9C,MAAM,EAAE,EAAE,EAAE,kCAAkC;YAC9C,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,GAAG,EAAE,sBAAsB;SAC3C,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,CAAC;YAC7C,MAAM,EAAE,EAAE,EAAE,gCAAgC;YAC5C,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,EAAE;SAClB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAE,MAAyB,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,UAAU,CAAC,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,UAAU,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAE,MAAyB,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM,CAAC,IAAI,CAAC,kDAAkD,IAAI,UAAU,CAAC,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,wCAAwC,IAAI,UAAU,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAE,MAAyB,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM,CAAC,IAAI,CAAC,gDAAgD,IAAI,UAAU,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,8CAA8C,IAAI,UAAU,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QAKtB,MAAM,CAAC,YAAY,EAAE,eAAe,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACxE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC;YAClC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC;YAC3C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC;SACzC,CAAC,CAAC;QAEH,OAAO;YACL,SAAS,EAAE;gBACT,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAI,YAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM;gBACpH,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,YAAY,EAAE,YAAY,IAAI,CAAC,CAAC,CAAC;aACpE;YACD,YAAY,EAAE;gBACZ,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAI,eAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM;gBAChI,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,CAAC,CAAC;aACvE;YACD,WAAW,EAAE;gBACX,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAI,cAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM;gBAC5H,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc,EAAE,YAAY,IAAI,CAAC,CAAC,CAAC;aACtE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC;YACrC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC;YAC9C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,YAAY,CAAC;SAC5C,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACnC,CAAC;CACF;AAED,+BAA+B;AAC/B,MAAM,CAAC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC"}