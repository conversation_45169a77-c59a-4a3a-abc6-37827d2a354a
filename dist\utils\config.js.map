{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/utils/config.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAChC,OAAO,EAAgB,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAErE,6BAA6B;AAC7B,MAAM,EAAE,CAAC;AAET;;GAEG;AACH,MAAM,UAAU,UAAU;IACxB,MAAM,SAAS,GAAG;QAChB,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,EAAE,EAAE,CAAC;QAC9C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,WAAW;QACrC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;QAC9C,SAAS,EAAE;YACT,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC;YACtD,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC;YAC/C,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,IAAI,EAAE,EAAE,CAAC;YACjE,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,GAAG,EAAE,EAAE,CAAC;SAC9E;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM;YAC9C,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,KAAK,EAAE,EAAE,CAAC;YAC5E,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;SACjE;QACD,WAAW,EAAE;YACX,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,OAAO;YAClE,0BAA0B,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,KAAK,MAAM;YAC/E,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,KAAK,EAAE,EAAE,CAAC;YACjE,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,KAAK,EAAE,EAAE,CAAC;SAC9D;KACF,CAAC;IAEF,yBAAyB;IACzB,MAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAEvD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACzE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClD,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,UAAU,EAAE,CAAC"}