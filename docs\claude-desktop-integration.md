# Claude Desktop Entegrasyonu

Bu dokümanda MCP Screen Streaming Server'ın <PERSON> ile nasıl entegre edileceği açıklanmaktadır.

## 🔧 Kurulum

### 1. Server'ı Hazırlama

```bash
# Projeyi build edin
npm run build

# Global olarak yükleyin (opsiyonel)
npm install -g .
```

### 2. <PERSON> Konfigürasyonu

Claude Desktop'ın ayarlarını açın:
- **macOS**: <PERSON> → Settings → Developer → Edit Config
- **Windows**: <PERSON> → Settings → Developer → Edit Config

`claude_desktop_config.json` dosyasını düzenleyin:

#### <PERSON><PERSON>
```json
{
  "mcpServers": {
    "screen-streaming": {
      "command": "node",
      "args": ["dist/index.js"],
      "cwd": "/path/to/mcp-screen-streaming"
    }
  }
}
```

#### NPM Package Kurulumu
```json
{
  "mcpServers": {
    "screen-streaming": {
      "command": "npx",
      "args": ["-y", "mcp-screen-streaming"]
    }
  }
}
```

#### Gelişmiş Konfigürasyon
```json
{
  "mcpServers": {
    "screen-streaming": {
      "command": "node",
      "args": ["dist/index.js"],
      "cwd": "/path/to/mcp-screen-streaming",
      "env": {
        "LOG_LEVEL": "info",
        "DEFAULT_QUALITY": "85",
        "ENABLE_DIFF_DETECTION": "true"
      }
    }
  }
}
```

### 3. Claude Desktop'ı Yeniden Başlatma

Konfigürasyon değişikliklerinden sonra Claude Desktop'ı tamamen kapatıp yeniden açın.

## 🎯 Kullanım Senaryoları

### Temel Ekran Görüntüsü Alma

```
Claude'a şunu söyleyin:
"Ekranımın bir görüntüsünü al ve analiz et"
```

Claude şu tool'u çağıracak:
```json
{
  "name": "get_screenshot",
  "arguments": {
    "quality": 80,
    "format": "jpeg"
  }
}
```

### Belirli Monitör Seçimi

```
"İkinci monitörümün görüntüsünü al"
```

Claude önce monitörleri listeleyecek:
```json
{
  "name": "list_monitors",
  "arguments": {}
}
```

Sonra belirli monitörü yakalayacak:
```json
{
  "name": "get_screenshot",
  "arguments": {
    "monitor": 1,
    "quality": 80
  }
}
```

### Bölgesel Ekran Yakalama

```
"Ekranımın sol üst köşesinin 800x600 piksellik kısmını yakala"
```

```json
{
  "name": "get_screenshot",
  "arguments": {
    "region": {
      "x": 0,
      "y": 0,
      "width": 800,
      "height": 600
    },
    "quality": 90
  }
}
```

### Quasi-Real-Time Monitoring

```
"Ekranımı 30 saniye boyunca her 5 saniyede bir kontrol et"
```

Claude bu işlemi birden fazla get_screenshot çağrısı ile gerçekleştirecek.

## 🔍 Troubleshooting

### Server Görünmüyor

1. **Konfigürasyon Kontrolü**
   ```bash
   # Konfigürasyon dosyasının varlığını kontrol edin
   # macOS:
   cat ~/Library/Application\ Support/Claude/claude_desktop_config.json
   
   # Windows:
   type "%APPDATA%\Claude\claude_desktop_config.json"
   ```

2. **Manuel Test**
   ```bash
   # Server'ı manuel olarak çalıştırın
   cd /path/to/mcp-screen-streaming
   node dist/index.js
   ```

3. **Log Kontrolü**
   ```bash
   # Claude Desktop loglarını kontrol edin
   # macOS:
   tail -f ~/Library/Logs/Claude/mcp*.log
   
   # Windows:
   type "%APPDATA%\Claude\logs\mcp*.log"
   ```

### Tool Çağrıları Başarısız

1. **Ekran Yakalama Kütüphanesi**
   ```bash
   # screenshot-desktop yükleyin
   npm install screenshot-desktop
   
   # veya robotjs
   npm install robotjs
   ```

2. **İzinler**
   - macOS'ta Screen Recording izni gerekebilir
   - Windows'ta UAC izinleri kontrol edin

3. **Memory Limitleri**
   ```env
   # .env dosyasında
   MEMORY_LIMIT_MB=1024
   DEFAULT_QUALITY=70
   ```

### Büyük Image Sorunları

1. **Kalite Azaltma**
   ```json
   {
     "name": "get_screenshot",
     "arguments": {
       "quality": 50,
       "format": "jpeg"
     }
   }
   ```

2. **Bölgesel Yakalama**
   ```json
   {
     "name": "get_screenshot",
     "arguments": {
       "region": {
         "x": 0,
         "y": 0,
         "width": 1280,
         "height": 720
       }
     }
   }
   ```

## 📊 Performans Optimizasyonu

### Claude Desktop İçin Önerilen Ayarlar

```env
# .env dosyası
DEFAULT_QUALITY=75
ENABLE_DIFF_DETECTION=true
MEMORY_LIMIT_MB=512
LOG_LEVEL=warn
```

### Kullanım Patterns

1. **Tek Seferlik Analiz**
   - `get_screenshot` kullanın
   - Yüksek kalite (85-95)

2. **Monitoring**
   - Düşük kalite (60-75)
   - Bölgesel yakalama
   - Düzenli aralıklarla

3. **Debugging**
   - Orta kalite (75-85)
   - Tam ekran
   - Gerektiğinde

## 🚀 Gelişmiş Özellikler

### Custom Prompts

Claude'a özel komutlar öğretebilirsiniz:

```
"Ekran monitoring" dediğimde:
1. Mevcut monitörleri listele
2. Her monitörün görüntüsünü al
3. Görüntüleri analiz et ve özet çıkar
```

### Automation Workflows

```
"Sunum modu" dediğimde:
1. Ana monitörün görüntüsünü al
2. Sunum yazılımı açık mı kontrol et
3. Slide içeriğini analiz et
4. Sunum önerileri ver
```

## 🔒 Güvenlik Notları

1. **İzinler**
   - Claude Desktop user permissions ile çalışır
   - Ekran yakalama izinleri gerekebilir

2. **Data Privacy**
   - Ekran görüntüleri local olarak işlenir
   - Claude'a gönderilen data base64 encoded
   - Hassas bilgiler için region filtering kullanın

3. **Rate Limiting**
   - Server otomatik rate limiting yapar
   - Aşırı kullanımda geçici blokaj

## 📈 Monitoring

### Server Status

```
"Screen streaming server durumu nedir?"
```

Claude `get_stream_status` tool'unu çağırarak server durumunu rapor edecek.

### Performance Metrics

Server performance metrics'leri log dosyalarında bulunabilir:

```bash
# Debug mode'da çalıştırın
LOG_LEVEL=debug node dist/index.js
```

## 🔄 Updates

Server güncellemeleri için:

```bash
# NPM package güncellemesi
npm update -g mcp-screen-streaming

# Yerel geliştirme
git pull
npm run build
```

Claude Desktop'ı yeniden başlatmayı unutmayın.
