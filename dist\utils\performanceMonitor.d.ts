import { EventEmitter } from 'events';
import { memoryManager } from './memoryManager.js';
/**
 * Performance monitoring and metrics collection
 */
export declare class PerformanceMonitor extends EventEmitter {
    private metrics;
    private startTimes;
    private counters;
    private monitoringInterval;
    constructor();
    /**
     * Start performance monitoring
     */
    private startMonitoring;
    /**
     * Stop performance monitoring
     */
    stopMonitoring(): void;
    /**
     * Start timing an operation
     */
    startTimer(operation: string): void;
    /**
     * End timing an operation and record the duration
     */
    endTimer(operation: string): number;
    /**
     * Record a metric value
     */
    recordMetric(name: string, value: number): void;
    /**
     * Increment a counter
     */
    incrementCounter(name: string, amount?: number): void;
    /**
     * Get counter value
     */
    getCounter(name: string): number;
    /**
     * Reset a counter
     */
    resetCounter(name: string): void;
    /**
     * Get statistics for a metric
     */
    getMetricStats(name: string): {
        count: number;
        min: number;
        max: number;
        average: number;
        median: number;
        p95: number;
        latest: number;
    } | null;
    /**
     * Collect system-level metrics
     */
    private collectSystemMetrics;
    /**
     * Get all performance metrics
     */
    getAllMetrics(): {
        metrics: Record<string, any>;
        counters: Record<string, number>;
        system: {
            uptime: number;
            memory: ReturnType<typeof memoryManager.getMemoryStats>;
            nodeVersion: string;
            platform: string;
        };
    };
    /**
     * Get performance summary
     */
    getPerformanceSummary(): {
        status: 'healthy' | 'warning' | 'critical';
        issues: string[];
        recommendations: string[];
        keyMetrics: {
            averageResponseTime: number | null;
            errorRate: number;
            memoryUsage: number;
            activeStreams: number;
        };
    };
    /**
     * Clear all metrics and counters
     */
    clearMetrics(): void;
    /**
     * Cleanup resources
     */
    cleanup(): void;
}
export declare const performanceMonitor: PerformanceMonitor;
//# sourceMappingURL=performanceMonitor.d.ts.map