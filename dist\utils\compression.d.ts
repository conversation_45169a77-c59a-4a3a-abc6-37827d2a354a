export interface CompressionOptions {
    quality: number;
    format: 'jpeg' | 'png' | 'webp';
    progressive?: boolean;
    optimizeScans?: boolean;
    mozjpeg?: boolean;
}
export interface CompressionResult {
    buffer: Buffer;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    format: string;
}
/**
 * Advanced image compression utilities using Sharp
 */
export declare class ImageCompressor {
    /**
     * Compress image buffer with specified options
     */
    static compress(inputBuffer: Buffer, options: CompressionOptions): Promise<CompressionResult>;
    /**
     * Auto-optimize image based on content analysis
     */
    static autoOptimize(inputBuffer: Buffer, targetSizeKB?: number, maxQuality?: number): Promise<CompressionResult>;
    /**
     * Adaptive compression based on content type
     */
    static adaptiveCompress(inputBuffer: Buffer, contentType: 'screenshot' | 'photo' | 'diagram' | 'text', quality?: number): Promise<CompressionResult>;
    /**
     * Resize image while maintaining aspect ratio
     */
    static resizeAndCompress(inputBuffer: <PERSON>uffer, maxWidth: number, maxHeight: number, options: CompressionOptions): Promise<CompressionResult>;
}
//# sourceMappingURL=compression.d.ts.map