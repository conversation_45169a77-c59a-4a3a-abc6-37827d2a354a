import { ScreenCapture } from '../server/screenCapture.js';
import { logger } from '../utils/logger.js';
/**
 * List monitors tool handler
 */
export async function listMonitorsTool() {
    try {
        logger.info('Listing available monitors');
        const screenCapture = new ScreenCapture();
        // Get monitor information
        const monitors = await screenCapture.getMonitors();
        const capabilities = screenCapture.getCapabilities();
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        monitors,
                        capabilities,
                        totalMonitors: monitors.length,
                        primaryMonitor: monitors.find(m => m.primary)?.id || 0
                    }, null, 2)
                }
            ]
        };
    }
    catch (error) {
        logger.error('Failed to list monitors', error);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                        code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
                    }, null, 2)
                }
            ],
            isError: true
        };
    }
}
//# sourceMappingURL=listMonitors.js.map