/**
 * Base64 encoding/decoding utilities with optimization
 */
/**
 * Encode buffer to base64 with optional chunking for large data
 */
export function encodeBase64(buffer, chunkSize) {
    if (!chunkSize || buffer.length <= chunkSize) {
        return buffer.toString('base64');
    }
    // For very large buffers, process in chunks to avoid memory issues
    let result = '';
    for (let i = 0; i < buffer.length; i += chunkSize) {
        const chunk = buffer.subarray(i, i + chunkSize);
        result += chunk.toString('base64');
    }
    return result;
}
/**
 * Decode base64 string to buffer
 */
export function decodeBase64(base64String) {
    return Buffer.from(base64String, 'base64');
}
/**
 * Get base64 data size in bytes
 */
export function getBase64Size(base64String) {
    // Remove data URL prefix if present
    const cleanBase64 = base64String.replace(/^data:[^;]+;base64,/, '');
    // Calculate size: each base64 character represents 6 bits
    // Padding characters ('=') don't count toward data size
    const paddingCount = (cleanBase64.match(/=/g) || []).length;
    return Math.floor((cleanBase64.length * 6) / 8) - paddingCount;
}
/**
 * Create data URL from buffer and MIME type
 */
export function createDataURL(buffer, mimeType) {
    const base64 = buffer.toString('base64');
    return `data:${mimeType};base64,${base64}`;
}
/**
 * Extract MIME type from data URL
 */
export function extractMimeType(dataURL) {
    const match = dataURL.match(/^data:([^;]+);base64,/);
    return match ? match[1] : null;
}
/**
 * Validate base64 string
 */
export function isValidBase64(str) {
    try {
        // Remove data URL prefix if present
        const cleanBase64 = str.replace(/^data:[^;]+;base64,/, '');
        // Check if it's valid base64
        return Buffer.from(cleanBase64, 'base64').toString('base64') === cleanBase64;
    }
    catch {
        return false;
    }
}
/**
 * Compress base64 string by removing unnecessary whitespace and formatting
 */
export function compressBase64(base64String) {
    return base64String.replace(/\s+/g, '');
}
/**
 * Split large base64 string into chunks for streaming
 */
export function chunkBase64(base64String, chunkSize = 1024) {
    const chunks = [];
    for (let i = 0; i < base64String.length; i += chunkSize) {
        chunks.push(base64String.substring(i, i + chunkSize));
    }
    return chunks;
}
//# sourceMappingURL=base64Utils.js.map