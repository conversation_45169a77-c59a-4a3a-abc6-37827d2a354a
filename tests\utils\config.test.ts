import { describe, it, expect, beforeEach } from 'vitest';
import { loadConfig } from '../../src/utils/config.js';

describe('Config Utils', () => {
  beforeEach(() => {
    // Reset environment variables
    delete process.env.PORT;
    delete process.env.HOST;
    delete process.env.DEFAULT_FPS;
    delete process.env.MAX_FPS;
    delete process.env.DEFAULT_QUALITY;
  });

  describe('loadConfig', () => {
    it('should load default configuration', () => {
      const config = loadConfig();
      
      expect(config.port).toBe(3000);
      expect(config.host).toBe('localhost');
      expect(config.streaming.defaultFps).toBe(1);
      expect(config.streaming.maxFps).toBe(10);
      expect(config.streaming.defaultQuality).toBe(80);
      expect(config.security.enableAuth).toBe(false); // Default when not set
    });

    it('should load configuration from environment variables', () => {
      process.env.PORT = '8080';
      process.env.HOST = '0.0.0.0';
      process.env.DEFAULT_FPS = '2';
      process.env.MAX_FPS = '5';
      process.env.DEFAULT_QUALITY = '90';
      process.env.ENABLE_AUTH = 'true';

      const config = loadConfig();
      
      expect(config.port).toBe(8080);
      expect(config.host).toBe('0.0.0.0');
      expect(config.streaming.defaultFps).toBe(2);
      expect(config.streaming.maxFps).toBe(5);
      expect(config.streaming.defaultQuality).toBe(90);
      expect(config.security.enableAuth).toBe(true);
    });

    it('should validate configuration values', () => {
      process.env.PORT = 'invalid';
      process.env.DEFAULT_FPS = '-1';
      
      expect(() => loadConfig()).toThrow('Invalid server configuration');
    });

    it('should handle allowed origins configuration', () => {
      process.env.ALLOWED_ORIGINS = 'http://localhost:3000,https://example.com';
      
      const config = loadConfig();
      
      expect(config.security.allowedOrigins).toEqual([
        'http://localhost:3000',
        'https://example.com'
      ]);
    });
  });
});
