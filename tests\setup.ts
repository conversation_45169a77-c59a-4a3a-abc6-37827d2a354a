import { beforeAll, afterAll } from 'vitest';
import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Global test setup
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests
  
  // Mock global.gc if not available
  if (!global.gc) {
    global.gc = () => {
      // Mock implementation for tests
    };
  }
});

// Global test cleanup
afterAll(async () => {
  // Cleanup any global resources
});
