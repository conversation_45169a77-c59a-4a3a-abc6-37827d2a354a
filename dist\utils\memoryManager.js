import { logger } from './logger.js';
import { serverConfig } from './config.js';
/**
 * Memory management utilities to prevent memory leaks and optimize performance
 */
export class MemoryManager {
    memoryCheckInterval = null;
    memoryLimitBytes;
    gcThreshold;
    lastGCTime = 0;
    memoryHistory = [];
    constructor() {
        this.memoryLimitBytes = serverConfig.performance.memoryLimitMb * 1024 * 1024;
        this.gcThreshold = this.memoryLimitBytes * 0.8; // Trigger GC at 80% of limit
        this.startMemoryMonitoring();
    }
    /**
     * Start memory monitoring
     */
    startMemoryMonitoring() {
        this.memoryCheckInterval = setInterval(() => {
            this.checkMemoryUsage();
        }, 30000); // Check every 30 seconds
        logger.info('Memory monitoring started', {
            memoryLimitMB: serverConfig.performance.memoryLimitMb,
            gcThresholdMB: Math.round(this.gcThreshold / 1024 / 1024)
        });
    }
    /**
     * Stop memory monitoring
     */
    stopMemoryMonitoring() {
        if (this.memoryCheckInterval) {
            clearInterval(this.memoryCheckInterval);
            this.memoryCheckInterval = null;
            logger.info('Memory monitoring stopped');
        }
    }
    /**
     * Check current memory usage and trigger GC if needed
     */
    checkMemoryUsage() {
        const memUsage = process.memoryUsage();
        const heapUsed = memUsage.heapUsed;
        const heapTotal = memUsage.heapTotal;
        const external = memUsage.external;
        const rss = memUsage.rss;
        // Add to history for trend analysis
        this.memoryHistory.push(heapUsed);
        if (this.memoryHistory.length > 20) {
            this.memoryHistory.shift(); // Keep only last 20 measurements
        }
        logger.debug('Memory usage', {
            heapUsedMB: Math.round(heapUsed / 1024 / 1024),
            heapTotalMB: Math.round(heapTotal / 1024 / 1024),
            externalMB: Math.round(external / 1024 / 1024),
            rssMB: Math.round(rss / 1024 / 1024),
            limitMB: serverConfig.performance.memoryLimitMb
        });
        // Check if memory limit is exceeded
        if (heapUsed > this.memoryLimitBytes) {
            logger.warn('Memory limit exceeded', {
                currentMB: Math.round(heapUsed / 1024 / 1024),
                limitMB: serverConfig.performance.memoryLimitMb
            });
            this.forceGarbageCollection();
            this.emitMemoryWarning('MEMORY_LIMIT_EXCEEDED', heapUsed);
        }
        // Check if GC threshold is reached
        else if (heapUsed > this.gcThreshold && this.shouldTriggerGC()) {
            logger.info('Triggering garbage collection', {
                currentMB: Math.round(heapUsed / 1024 / 1024),
                thresholdMB: Math.round(this.gcThreshold / 1024 / 1024)
            });
            this.forceGarbageCollection();
        }
    }
    /**
     * Determine if GC should be triggered based on memory trends
     */
    shouldTriggerGC() {
        const now = Date.now();
        const timeSinceLastGC = now - this.lastGCTime;
        // Don't trigger GC too frequently (minimum 2 minutes between GC)
        if (timeSinceLastGC < 120000) {
            return false;
        }
        // Check if memory usage is trending upward
        if (this.memoryHistory.length >= 3) {
            const recent = this.memoryHistory.slice(-3);
            const isIncreasing = recent[2] > recent[1] && recent[1] > recent[0];
            return isIncreasing;
        }
        return true;
    }
    /**
     * Force garbage collection
     */
    forceGarbageCollection() {
        if (global.gc) {
            const beforeGC = process.memoryUsage().heapUsed;
            global.gc();
            const afterGC = process.memoryUsage().heapUsed;
            const freed = beforeGC - afterGC;
            this.lastGCTime = Date.now();
            logger.info('Garbage collection completed', {
                freedMB: Math.round(freed / 1024 / 1024),
                beforeMB: Math.round(beforeGC / 1024 / 1024),
                afterMB: Math.round(afterGC / 1024 / 1024)
            });
        }
        else {
            logger.warn('Garbage collection not available. Start with --expose-gc flag.');
        }
    }
    /**
     * Emit memory warning event
     */
    emitMemoryWarning(type, currentUsage) {
        // This could be extended to emit events to external monitoring systems
        logger.audit('memory_warning', {
            type,
            currentUsageMB: Math.round(currentUsage / 1024 / 1024),
            limitMB: serverConfig.performance.memoryLimitMb,
            timestamp: Date.now()
        });
    }
    /**
     * Get current memory statistics
     */
    getMemoryStats() {
        const memUsage = process.memoryUsage();
        const percentage = (memUsage.heapUsed / this.memoryLimitBytes) * 100;
        // Calculate trend
        let trend = 'stable';
        if (this.memoryHistory.length >= 3) {
            const recent = this.memoryHistory.slice(-3);
            if (recent[2] > recent[0] * 1.1)
                trend = 'increasing';
            else if (recent[2] < recent[0] * 0.9)
                trend = 'decreasing';
        }
        return {
            current: memUsage,
            limit: this.memoryLimitBytes,
            usage: {
                percentage: Math.round(percentage * 100) / 100,
                trend
            },
            gc: {
                lastGCTime: this.lastGCTime,
                timeSinceLastGC: Date.now() - this.lastGCTime
            }
        };
    }
    /**
     * Clean up resources
     */
    cleanup() {
        this.stopMemoryMonitoring();
        this.memoryHistory = [];
    }
    /**
     * Check if memory usage is healthy
     */
    isMemoryHealthy() {
        const stats = this.getMemoryStats();
        return stats.usage.percentage < 80 && stats.usage.trend !== 'increasing';
    }
    /**
     * Get memory recommendations
     */
    getMemoryRecommendations() {
        const stats = this.getMemoryStats();
        const recommendations = [];
        if (stats.usage.percentage > 90) {
            recommendations.push('Memory usage is critically high. Consider stopping some streams.');
        }
        else if (stats.usage.percentage > 80) {
            recommendations.push('Memory usage is high. Monitor closely.');
        }
        if (stats.usage.trend === 'increasing') {
            recommendations.push('Memory usage is trending upward. Check for memory leaks.');
        }
        if (!global.gc) {
            recommendations.push('Start server with --expose-gc flag for better memory management.');
        }
        if (recommendations.length === 0) {
            recommendations.push('Memory usage is healthy.');
        }
        return recommendations;
    }
}
// Global memory manager instance
export const memoryManager = new MemoryManager();
//# sourceMappingURL=memoryManager.js.map