import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { logger } from '../utils/logger.js';
import { serverConfig } from '../utils/config.js';
import { StreamManager } from './streamManager.js';
import { rateLimiter } from '../utils/rateLimiter.js';
import { performanceMonitor } from '../utils/performanceMonitor.js';
import { memoryManager } from '../utils/memoryManager.js';
// Import tool handlers
import { startStreamTool } from '../tools/startStream.js';
import { stopStreamTool } from '../tools/stopStream.js';
import { configureStreamTool } from '../tools/configureStream.js';
import { getScreenshotTool } from '../tools/getScreenshot.js';
import { listMonitorsTool } from '../tools/listMonitors.js';
import { getStreamStatusTool } from '../tools/getStreamStatus.js';
/**
 * MCP Screen Streaming Server
 * Provides real-time screen capture and streaming capabilities for AI assistants
 */
export class MCPScreenStreamingServer {
    server;
    streamManager;
    tools;
    constructor() {
        this.server = new Server({
            name: 'mcp-screen-streaming',
            version: '1.0.0'
        }, {
            capabilities: {
                tools: {}
            }
        });
        this.streamManager = new StreamManager();
        this.tools = this.initializeTools();
        this.setupHandlers();
    }
    /**
     * Initialize all available tools
     */
    initializeTools() {
        return [
            {
                name: 'start_screen_stream',
                description: 'Start real-time screen streaming with configurable parameters',
                inputSchema: {
                    type: 'object',
                    properties: {
                        fps: {
                            type: 'number',
                            minimum: 0.1,
                            maximum: serverConfig.streaming.maxFps,
                            default: serverConfig.streaming.defaultFps,
                            description: 'Frames per second for the stream'
                        },
                        quality: {
                            type: 'number',
                            minimum: 1,
                            maximum: 100,
                            default: serverConfig.streaming.defaultQuality,
                            description: 'JPEG quality (1-100)'
                        },
                        monitor: {
                            type: 'number',
                            minimum: 0,
                            default: 0,
                            description: 'Monitor ID to capture'
                        },
                        region: {
                            type: 'object',
                            properties: {
                                x: { type: 'number', minimum: 0 },
                                y: { type: 'number', minimum: 0 },
                                width: { type: 'number', minimum: 1 },
                                height: { type: 'number', minimum: 1 }
                            },
                            description: 'Specific region to capture'
                        },
                        enableDiffDetection: {
                            type: 'boolean',
                            default: serverConfig.performance.enableDiffDetection,
                            description: 'Enable differential frame detection for optimization'
                        },
                        format: {
                            type: 'string',
                            enum: ['jpeg', 'png', 'webp'],
                            default: 'jpeg',
                            description: 'Image format for captured frames'
                        }
                    }
                }
            },
            {
                name: 'stop_screen_stream',
                description: 'Stop an active screen stream',
                inputSchema: {
                    type: 'object',
                    properties: {
                        streamId: {
                            type: 'string',
                            description: 'ID of the stream to stop'
                        }
                    },
                    required: ['streamId']
                }
            },
            {
                name: 'configure_stream',
                description: 'Modify settings of an active stream',
                inputSchema: {
                    type: 'object',
                    properties: {
                        streamId: {
                            type: 'string',
                            description: 'ID of the stream to configure'
                        },
                        fps: {
                            type: 'number',
                            minimum: 0.1,
                            maximum: serverConfig.streaming.maxFps,
                            description: 'New frames per second'
                        },
                        quality: {
                            type: 'number',
                            minimum: 1,
                            maximum: 100,
                            description: 'New JPEG quality'
                        },
                        enableDiffDetection: {
                            type: 'boolean',
                            description: 'Enable/disable differential detection'
                        }
                    },
                    required: ['streamId']
                }
            },
            {
                name: 'get_screenshot',
                description: 'Capture a single screenshot',
                inputSchema: {
                    type: 'object',
                    properties: {
                        monitor: {
                            type: 'number',
                            minimum: 0,
                            default: 0,
                            description: 'Monitor ID to capture'
                        },
                        region: {
                            type: 'object',
                            properties: {
                                x: { type: 'number', minimum: 0 },
                                y: { type: 'number', minimum: 0 },
                                width: { type: 'number', minimum: 1 },
                                height: { type: 'number', minimum: 1 }
                            },
                            description: 'Specific region to capture'
                        },
                        quality: {
                            type: 'number',
                            minimum: 1,
                            maximum: 100,
                            default: serverConfig.streaming.defaultQuality,
                            description: 'Image quality'
                        },
                        format: {
                            type: 'string',
                            enum: ['jpeg', 'png', 'webp'],
                            default: 'jpeg',
                            description: 'Image format'
                        }
                    }
                }
            },
            {
                name: 'list_monitors',
                description: 'Get information about available monitors',
                inputSchema: {
                    type: 'object',
                    properties: {}
                }
            },
            {
                name: 'get_stream_status',
                description: 'Get status and statistics of active streams',
                inputSchema: {
                    type: 'object',
                    properties: {
                        streamId: {
                            type: 'string',
                            description: 'Specific stream ID (optional, returns all if not provided)'
                        }
                    }
                }
            }
        ];
    }
    /**
     * Setup MCP request handlers
     */
    setupHandlers() {
        // List available tools
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            logger.debug('Received list_tools request');
            return {
                tools: this.tools
            };
        });
        // Handle tool calls
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            logger.info(`Tool called: ${name}`, args);
            logger.audit('tool_call', { toolName: name, arguments: args });
            // Performance monitoring
            performanceMonitor.startTimer(`tool_${name}`);
            performanceMonitor.incrementCounter('total_requests');
            try {
                // Rate limiting
                await rateLimiter.checkToolCallLimit();
                // Additional rate limiting for specific tools
                if (name === 'start_screen_stream') {
                    await rateLimiter.checkStreamStartLimit();
                }
                else if (name === 'get_screenshot') {
                    await rateLimiter.checkScreenshotLimit();
                }
                let result;
                switch (name) {
                    case 'start_screen_stream':
                        result = await startStreamTool(this.streamManager, args);
                        break;
                    case 'stop_screen_stream':
                        result = await stopStreamTool(this.streamManager, args);
                        break;
                    case 'configure_stream':
                        result = await configureStreamTool(this.streamManager, args);
                        break;
                    case 'get_screenshot':
                        result = await getScreenshotTool(args);
                        break;
                    case 'list_monitors':
                        result = await listMonitorsTool();
                        break;
                    case 'get_stream_status':
                        result = await getStreamStatusTool(this.streamManager, args);
                        break;
                    default:
                        throw new Error(`Unknown tool: ${name}`);
                }
                // Record successful execution
                const executionTime = performanceMonitor.endTimer(`tool_${name}`);
                logger.debug(`Tool ${name} executed successfully in ${executionTime}ms`);
                return result;
            }
            catch (error) {
                // Record error and execution time
                performanceMonitor.incrementCounter('total_errors');
                performanceMonitor.endTimer(`tool_${name}`);
                logger.error(`Tool execution failed: ${name}`, error);
                throw error;
            }
        });
    }
    /**
     * Start the MCP server
     */
    async start() {
        const transport = new StdioServerTransport();
        logger.info('Starting MCP Screen Streaming Server', {
            version: '1.0.0',
            config: serverConfig
        });
        await this.server.connect(transport);
        logger.info('MCP Server started successfully');
    }
    /**
     * Stop the server and cleanup resources
     */
    async stop() {
        logger.info('Stopping MCP Server...');
        // Stop all active streams
        await this.streamManager.stopAllStreams();
        // Cleanup performance monitoring
        performanceMonitor.cleanup();
        memoryManager.cleanup();
        // Close server connection
        await this.server.close();
        logger.info('MCP Server stopped');
    }
}
// Handle graceful shutdown
process.on('SIGINT', async () => {
    logger.info('Received SIGINT, shutting down gracefully...');
    process.exit(0);
});
process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});
//# sourceMappingURL=index.js.map