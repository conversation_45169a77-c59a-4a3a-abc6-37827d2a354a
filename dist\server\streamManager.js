import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { StreamError, StreamConfigSchema } from '../types/index.js';
import { logger } from '../utils/logger.js';
import { serverConfig } from '../utils/config.js';
import { ScreenCapture } from './screenCapture.js';
/**
 * Manages multiple concurrent screen streams with optimization and monitoring
 */
export class StreamManager extends EventEmitter {
    streams = new Map();
    screenCapture;
    maxStreams;
    constructor() {
        super();
        this.screenCapture = new ScreenCapture();
        this.maxStreams = serverConfig.streaming.maxConcurrentStreams;
        // Setup cleanup interval for inactive streams
        setInterval(() => this.cleanupInactiveStreams(), 30000); // Every 30 seconds
    }
    /**
     * Start a new screen stream
     */
    async startStream(config) {
        // Check concurrent stream limit
        if (this.streams.size >= this.maxStreams) {
            throw new StreamError(`Maximum concurrent streams limit reached (${this.maxStreams})`, 'MAX_STREAMS_EXCEEDED');
        }
        // Validate and merge with defaults
        const validatedConfig = StreamConfigSchema.parse({
            fps: config.fps ?? serverConfig.streaming.defaultFps,
            quality: config.quality ?? serverConfig.streaming.defaultQuality,
            monitor: config.monitor ?? 0,
            region: config.region,
            enableDiffDetection: config.enableDiffDetection ?? serverConfig.performance.enableDiffDetection,
            enableCompression: config.enableCompression ?? true,
            format: config.format ?? 'jpeg'
        });
        const streamId = uuidv4();
        const intervalMs = 1000 / validatedConfig.fps;
        logger.info(`Starting stream ${streamId}`, validatedConfig);
        const stream = {
            id: streamId,
            config: validatedConfig,
            interval: setInterval(() => this.captureFrame(streamId), intervalMs),
            stats: {
                totalFrames: 0,
                droppedFrames: 0,
                totalDataSent: 0,
                startTime: Date.now(),
                lastFrameTime: Date.now(),
                captureTimes: []
            },
            isActive: true
        };
        this.streams.set(streamId, stream);
        logger.audit('stream_started', {
            streamId,
            config: validatedConfig,
            activeStreams: this.streams.size
        });
        return streamId;
    }
    /**
     * Stop a specific stream
     */
    async stopStream(streamId) {
        const stream = this.streams.get(streamId);
        if (!stream) {
            throw new StreamError(`Stream not found: ${streamId}`, 'STREAM_NOT_FOUND', streamId);
        }
        clearInterval(stream.interval);
        stream.isActive = false;
        this.streams.delete(streamId);
        logger.info(`Stream stopped: ${streamId}`);
        logger.audit('stream_stopped', {
            streamId,
            stats: stream.stats,
            duration: Date.now() - stream.stats.startTime
        });
        this.emit('streamStopped', streamId);
        return true;
    }
    /**
     * Stop all active streams
     */
    async stopAllStreams() {
        const streamIds = Array.from(this.streams.keys());
        logger.info(`Stopping ${streamIds.length} active streams`);
        for (const streamId of streamIds) {
            await this.stopStream(streamId);
        }
    }
    /**
     * Configure an existing stream
     */
    async configureStream(streamId, newConfig) {
        const stream = this.streams.get(streamId);
        if (!stream) {
            throw new StreamError(`Stream not found: ${streamId}`, 'STREAM_NOT_FOUND', streamId);
        }
        // Merge new configuration
        const updatedConfig = StreamConfigSchema.parse({
            ...stream.config,
            ...newConfig
        });
        // Update stream configuration
        stream.config = updatedConfig;
        // Restart interval if FPS changed
        if (newConfig.fps && newConfig.fps !== stream.config.fps) {
            clearInterval(stream.interval);
            const intervalMs = 1000 / updatedConfig.fps;
            stream.interval = setInterval(() => this.captureFrame(streamId), intervalMs);
        }
        logger.info(`Stream configured: ${streamId}`, updatedConfig);
        logger.audit('stream_configured', { streamId, newConfig: updatedConfig });
        return true;
    }
    /**
     * Get status of streams
     */
    getStreamStatus(streamId) {
        if (streamId) {
            const stream = this.streams.get(streamId);
            if (!stream) {
                throw new StreamError(`Stream not found: ${streamId}`, 'STREAM_NOT_FOUND', streamId);
            }
            return this.buildStreamStatus(stream);
        }
        // Return all streams
        return Array.from(this.streams.values()).map(stream => this.buildStreamStatus(stream));
    }
    /**
     * Capture a frame for a specific stream
     */
    async captureFrame(streamId) {
        const stream = this.streams.get(streamId);
        if (!stream || !stream.isActive) {
            return;
        }
        const captureStart = Date.now();
        try {
            const screenshot = await this.screenCapture.capture({
                monitor: stream.config.monitor,
                region: stream.config.region,
                quality: stream.config.quality,
                format: stream.config.format
            });
            const captureTime = Date.now() - captureStart;
            // Diff detection optimization
            if (stream.config.enableDiffDetection && stream.lastFrameBuffer) {
                const isDifferent = await this.compareFrames(screenshot.buffer, stream.lastFrameBuffer);
                if (!isDifferent) {
                    // Skip this frame as it's identical to the previous one
                    return;
                }
            }
            // Update statistics
            stream.stats.totalFrames++;
            stream.stats.lastFrameTime = Date.now();
            stream.stats.totalDataSent += screenshot.buffer.length;
            stream.stats.captureTimes.push(captureTime);
            // Keep only last 10 capture times for rolling average
            if (stream.stats.captureTimes.length > 10) {
                stream.stats.captureTimes.shift();
            }
            // Store frame for diff detection
            if (stream.config.enableDiffDetection) {
                stream.lastFrameBuffer = screenshot.buffer;
            }
            // Create stream data
            const streamData = {
                streamId,
                timestamp: Date.now(),
                frameNumber: stream.stats.totalFrames,
                screenshot: screenshot.base64,
                metadata: {
                    width: screenshot.width,
                    height: screenshot.height,
                    format: screenshot.format,
                    size: screenshot.buffer.length,
                    compressionRatio: screenshot.compressionRatio,
                    captureTime
                }
            };
            // Emit stream data event
            this.emit('streamData', streamData);
            logger.debug(`Frame captured for stream ${streamId}`, {
                frameNumber: stream.stats.totalFrames,
                size: screenshot.buffer.length,
                captureTime
            });
        }
        catch (error) {
            stream.stats.droppedFrames++;
            logger.error(`Frame capture failed for stream ${streamId}`, error);
            // Stop stream if too many consecutive failures
            if (stream.stats.droppedFrames > 10) {
                logger.warn(`Stopping stream ${streamId} due to excessive failures`);
                await this.stopStream(streamId);
            }
        }
    }
    /**
     * Compare two frame buffers to detect changes
     */
    async compareFrames(buffer1, buffer2) {
        // Simple buffer comparison - can be enhanced with more sophisticated algorithms
        if (buffer1.length !== buffer2.length) {
            return true;
        }
        // Sample comparison for performance (check every 100th byte)
        const sampleSize = Math.min(1000, Math.floor(buffer1.length / 100));
        const step = Math.floor(buffer1.length / sampleSize);
        for (let i = 0; i < buffer1.length; i += step) {
            if (buffer1[i] !== buffer2[i]) {
                return true;
            }
        }
        return false;
    }
    /**
     * Build stream status object
     */
    buildStreamStatus(stream) {
        const now = Date.now();
        const uptime = (now - stream.stats.startTime) / 1000;
        const averageFps = stream.stats.totalFrames / uptime;
        const averageCaptureTime = stream.stats.captureTimes.length > 0
            ? stream.stats.captureTimes.reduce((a, b) => a + b, 0) / stream.stats.captureTimes.length
            : 0;
        return {
            streamId: stream.id,
            isActive: stream.isActive,
            config: stream.config,
            stats: {
                totalFrames: stream.stats.totalFrames,
                droppedFrames: stream.stats.droppedFrames,
                averageFps: Math.round(averageFps * 100) / 100,
                averageCaptureTime: Math.round(averageCaptureTime * 100) / 100,
                totalDataSent: stream.stats.totalDataSent,
                uptime: Math.round(uptime * 100) / 100
            },
            lastFrame: stream.stats.lastFrameTime
        };
    }
    /**
     * Cleanup inactive streams
     */
    cleanupInactiveStreams() {
        const now = Date.now();
        const inactiveThreshold = 5 * 60 * 1000; // 5 minutes
        for (const [streamId, stream] of this.streams) {
            if (!stream.isActive || (now - stream.stats.lastFrameTime) > inactiveThreshold) {
                logger.info(`Cleaning up inactive stream: ${streamId}`);
                this.stopStream(streamId).catch(error => {
                    logger.error(`Failed to cleanup stream ${streamId}`, error);
                });
            }
        }
    }
    /**
     * Get current stream count
     */
    getActiveStreamCount() {
        return this.streams.size;
    }
    /**
     * Check if a stream exists
     */
    hasStream(streamId) {
        return this.streams.has(streamId);
    }
}
//# sourceMappingURL=streamManager.js.map