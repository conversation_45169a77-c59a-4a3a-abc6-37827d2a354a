# Configuration Guide

Bu dokümanda MCP Screen Streaming Server'ın konfigürasyon seçenekleri detaylı olarak açıklanmaktadır.

## Konfigürasyon Yöntemleri

### 1. Environment Variables (.env dosyası)
En yaygın kullanılan yöntem. Proje kök dizininde `.env` dosyası oluşturun:

```bash
cp .env.example .env
```

### 2. Sistem Environment Variables
İşletim sistemi seviyesinde environment variable'lar tanımlayabilirsiniz:

```bash
# Linux/macOS
export PORT=3000
export DEFAULT_FPS=2

# Windows
set PORT=3000
set DEFAULT_FPS=2
```

### 3. Docker Environment
Docker container'da çalıştırırken:

```bash
docker run -e PORT=3000 -e DEFAULT_FPS=2 mcp-screen-streaming
```

## Konfigürasyon Kategorileri

### Server Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `PORT` | number | 3000 | Server port numarası |
| `HOST` | string | localhost | Server host adresi |
| `NODE_ENV` | string | development | Çalışma ortamı (development/production/test) |

**Örnek:**
```env
PORT=8080
HOST=0.0.0.0
NODE_ENV=production
```

### Streaming Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `DEFAULT_FPS` | number | 1 | Varsayılan FPS değeri |
| `MAX_FPS` | number | 10 | Maksimum izin verilen FPS |
| `DEFAULT_QUALITY` | number | 80 | Varsayılan görüntü kalitesi (1-100) |
| `MAX_CONCURRENT_STREAMS` | number | 5 | Maksimum eşzamanlı stream sayısı |

**Örnek:**
```env
DEFAULT_FPS=2
MAX_FPS=15
DEFAULT_QUALITY=90
MAX_CONCURRENT_STREAMS=10
```

**FPS Önerileri:**
- **1 FPS**: Düşük bant genişliği, temel monitoring
- **2-3 FPS**: Genel kullanım, iyi performans/kalite dengesi
- **5+ FPS**: Yüksek kalite, daha fazla kaynak kullanımı

### Security Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `ENABLE_AUTH` | boolean | true | Kimlik doğrulama aktif/pasif |
| `RATE_LIMIT_PER_MINUTE` | number | 100 | Dakika başına maksimum istek |
| `ALLOWED_ORIGINS` | string | * | İzin verilen origin'ler (virgülle ayrılmış) |

**Örnek:**
```env
ENABLE_AUTH=true
RATE_LIMIT_PER_MINUTE=200
ALLOWED_ORIGINS=http://localhost:3000,https://myapp.com
```

**Güvenlik Önerileri:**
- Production'da `ENABLE_AUTH=true` kullanın
- `ALLOWED_ORIGINS` ile belirli domain'leri sınırlayın
- Rate limiting değerlerini kullanım senaryonuza göre ayarlayın

### Performance Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `ENABLE_DIFF_DETECTION` | boolean | true | Değişiklik algılama optimizasyonu |
| `ENABLE_HARDWARE_ACCELERATION` | boolean | false | Hardware acceleration (GPU) |
| `MEMORY_LIMIT_MB` | number | 512 | Bellek kullanım sınırı (MB) |
| `CACHE_SIZE_MB` | number | 100 | Cache boyutu (MB) |

**Örnek:**
```env
ENABLE_DIFF_DETECTION=true
ENABLE_HARDWARE_ACCELERATION=true
MEMORY_LIMIT_MB=1024
CACHE_SIZE_MB=200
```

**Performans Önerileri:**
- `ENABLE_DIFF_DETECTION=true` ile %30-50 performans artışı
- GPU varsa `ENABLE_HARDWARE_ACCELERATION=true` kullanın
- Bellek sınırını sistem kapasitesine göre ayarlayın

### Logging Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `LOG_LEVEL` | string | info | Log seviyesi (error/warn/info/debug) |
| `ENABLE_AUDIT_LOG` | boolean | true | Güvenlik audit logları |

**Örnek:**
```env
LOG_LEVEL=debug
ENABLE_AUDIT_LOG=true
```

**Log Seviyeleri:**
- **error**: Sadece hatalar
- **warn**: Hatalar ve uyarılar
- **info**: Genel bilgiler (önerilen)
- **debug**: Detaylı debug bilgileri

### Screen Capture Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `CAPTURE_CURSOR` | boolean | true | Mouse cursor'unu yakala |
| `CAPTURE_AUDIO` | boolean | false | Ses yakalama (gelecek sürümde) |
| `DEFAULT_MONITOR` | number | 0 | Varsayılan monitör ID |

**Örnek:**
```env
CAPTURE_CURSOR=false
CAPTURE_AUDIO=false
DEFAULT_MONITOR=1
```

## Ortam Bazlı Konfigürasyonlar

### Development Environment
```env
NODE_ENV=development
LOG_LEVEL=debug
ENABLE_AUTH=false
RATE_LIMIT_PER_MINUTE=1000
DEFAULT_FPS=1
MEMORY_LIMIT_MB=256
```

### Production Environment
```env
NODE_ENV=production
LOG_LEVEL=info
ENABLE_AUTH=true
RATE_LIMIT_PER_MINUTE=100
DEFAULT_FPS=2
MEMORY_LIMIT_MB=1024
ALLOWED_ORIGINS=https://yourdomain.com
```

### Test Environment
```env
NODE_ENV=test
LOG_LEVEL=error
ENABLE_AUTH=false
RATE_LIMIT_PER_MINUTE=10000
DEFAULT_FPS=1
MEMORY_LIMIT_MB=128
MAX_CONCURRENT_STREAMS=2
```

## Konfigürasyon Doğrulama

Server başlatıldığında tüm konfigürasyon değerleri doğrulanır:

```typescript
// Geçersiz konfigürasyon örneği
PORT=invalid        // Hata: number bekleniyor
DEFAULT_FPS=-1      // Hata: minimum 0.1
MAX_FPS=0          // Hata: minimum 1
DEFAULT_QUALITY=150 // Hata: maksimum 100
```

## Dinamik Konfigürasyon

Bazı ayarlar runtime'da değiştirilebilir:

```javascript
// Stream-specific ayarlar
await streamManager.configureStream(streamId, {
  fps: 3,
  quality: 95,
  enableDiffDetection: false
});
```

## Konfigürasyon Best Practices

### 1. Güvenlik
- Production'da authentication aktif edin
- Rate limiting değerlerini konservatif tutun
- Allowed origins'i spesifik domain'lerle sınırlayın

### 2. Performans
- Diff detection'ı aktif tutun
- Memory limit'i sistem kapasitesinin %70'i olarak ayarlayın
- FPS değerini ihtiyacınıza göre optimize edin

### 3. Monitoring
- Log level'i production'da 'info' tutun
- Audit logging'i güvenlik için aktif edin
- Performance metrics'i takip edin

### 4. Scalability
- Concurrent stream limit'i server kapasitesine göre ayarlayın
- Cache size'ı bellek kullanımına göre optimize edin
- Hardware acceleration'ı mümkünse aktif edin

## Troubleshooting

### Yaygın Konfigürasyon Sorunları

1. **"Invalid server configuration" hatası**
   - Environment variable tiplerini kontrol edin
   - Numeric değerlerin geçerli aralıkta olduğunu doğrulayın

2. **"Rate limit exceeded" hatası**
   - `RATE_LIMIT_PER_MINUTE` değerini artırın
   - Client-side request frequency'sini azaltın

3. **"Memory limit exceeded" uyarısı**
   - `MEMORY_LIMIT_MB` değerini artırın
   - Aktif stream sayısını azaltın
   - Diff detection'ı aktif edin

4. **Düşük performans**
   - `ENABLE_DIFF_DETECTION=true` ayarlayın
   - FPS değerini azaltın
   - Image quality'yi düşürün
   - Hardware acceleration'ı deneyin

### Debug Konfigürasyonu

Sorun giderme için debug modunu aktif edin:

```env
LOG_LEVEL=debug
NODE_ENV=development
```

Bu mod detaylı log çıktıları sağlar ve performans metrics'lerini gösterir.
