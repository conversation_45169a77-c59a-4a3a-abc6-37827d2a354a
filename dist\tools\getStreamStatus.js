import { logger } from '../utils/logger.js';
import { z } from 'zod';
const GetStreamStatusInputSchema = z.object({
    streamId: z.string().optional()
});
/**
 * Get stream status tool handler
 */
export async function getStreamStatusTool(streamManager, args) {
    try {
        // Validate input arguments
        const { streamId } = GetStreamStatusInputSchema.parse(args);
        logger.info('Getting stream status', { streamId });
        // Get stream status
        const status = streamManager.getStreamStatus(streamId);
        const activeStreamCount = streamManager.getActiveStreamCount();
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        status,
                        summary: {
                            activeStreams: activeStreamCount,
                            requestedStream: streamId || 'all',
                            timestamp: Date.now()
                        }
                    }, null, 2)
                }
            ]
        };
    }
    catch (error) {
        logger.error('Failed to get stream status', error);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                        code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
                    }, null, 2)
                }
            ],
            isError: true
        };
    }
}
//# sourceMappingURL=getStreamStatus.js.map