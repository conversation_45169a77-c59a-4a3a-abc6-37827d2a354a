import { StopStreamInputSchema } from '../types/index.js';
import { logger } from '../utils/logger.js';
/**
 * Stop screen stream tool handler
 */
export async function stopStreamTool(streamManager, args) {
    try {
        // Validate input arguments
        const { streamId } = StopStreamInputSchema.parse(args);
        logger.info(`Stopping stream: ${streamId}`);
        // Get final status before stopping
        const finalStatus = streamManager.getStreamStatus(streamId);
        // Stop the stream
        const success = await streamManager.stopStream(streamId);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success,
                        streamId,
                        message: 'Screen stream stopped successfully',
                        finalStats: Array.isArray(finalStatus) ? finalStatus[0]?.stats : finalStatus.stats,
                        activeStreams: streamManager.getActiveStreamCount()
                    }, null, 2)
                }
            ]
        };
    }
    catch (error) {
        logger.error('Failed to stop stream', error);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                        code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
                    }, null, 2)
                }
            ],
            isError: true
        };
    }
}
//# sourceMappingURL=stopStream.js.map