import { EventEmitter } from 'events';
import { StreamConfig, StreamStatus } from '../types/index.js';
/**
 * Manages multiple concurrent screen streams with optimization and monitoring
 */
export declare class StreamManager extends EventEmitter {
    private streams;
    private screenCapture;
    private maxStreams;
    constructor();
    /**
     * Start a new screen stream
     */
    startStream(config: Partial<StreamConfig>): Promise<string>;
    /**
     * Stop a specific stream
     */
    stopStream(streamId: string): Promise<boolean>;
    /**
     * Stop all active streams
     */
    stopAllStreams(): Promise<void>;
    /**
     * Configure an existing stream
     */
    configureStream(streamId: string, newConfig: Partial<StreamConfig>): Promise<boolean>;
    /**
     * Get status of streams
     */
    getStreamStatus(streamId?: string): StreamStatus | StreamStatus[];
    /**
     * Capture a frame for a specific stream
     */
    private captureFrame;
    /**
     * Compare two frame buffers to detect changes
     */
    private compareFrames;
    /**
     * Build stream status object
     */
    private buildStreamStatus;
    /**
     * Cleanup inactive streams
     */
    private cleanupInactiveStreams;
    /**
     * Get current stream count
     */
    getActiveStreamCount(): number;
    /**
     * Check if a stream exists
     */
    hasStream(streamId: string): boolean;
}
//# sourceMappingURL=streamManager.d.ts.map