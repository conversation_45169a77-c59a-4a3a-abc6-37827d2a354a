{"name": "mcp-screen-streaming", "version": "1.0.0", "description": "MCP Server for real-time screen streaming with AI integration", "main": "dist/index.js", "type": "module", "bin": {"mcp-screen-streaming": "dist/index.js"}, "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "inspector": "npx @modelcontextprotocol/inspector dist/index.js"}, "keywords": ["mcp", "model-context-protocol", "screen-capture", "streaming", "ai", "automation"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "sharp": "^0.33.0", "ws": "^8.18.0", "express": "^4.19.0", "cors": "^2.8.5", "helmet": "^7.1.0", "rate-limiter-flexible": "^5.0.3", "zod": "^3.23.0", "uuid": "^10.0.0", "dotenv": "^16.4.0"}, "devDependencies": {"@types/node": "^20.14.0", "@types/express": "^4.17.21", "@types/ws": "^8.5.10", "@types/cors": "^2.8.17", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "eslint": "^8.57.0", "tsx": "^4.15.0", "typescript": "^5.4.0", "vitest": "^1.6.0", "@vitest/coverage-v8": "^1.6.0"}, "peerDependencies": {"screenshot-desktop": "^1.15.0", "robotjs": "^0.6.0"}, "peerDependenciesMeta": {"screenshot-desktop": {"optional": true}, "robotjs": {"optional": true}}}