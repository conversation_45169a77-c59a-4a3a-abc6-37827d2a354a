import sharp from 'sharp';
import { logger } from './logger.js';
/**
 * Advanced image compression utilities using Sharp
 */
export class ImageCompressor {
    /**
     * Compress image buffer with specified options
     */
    static async compress(inputBuffer, options) {
        const originalSize = inputBuffer.length;
        try {
            let image = sharp(inputBuffer);
            // Apply format-specific compression
            switch (options.format) {
                case 'jpeg':
                    image = image.jpeg({
                        quality: options.quality,
                        progressive: options.progressive ?? true,
                        optimizeScans: options.optimizeScans ?? true,
                        mozjpeg: options.mozjpeg ?? true
                    });
                    break;
                case 'png':
                    // Convert quality (1-100) to PNG compression level (0-9)
                    const compressionLevel = Math.floor((100 - options.quality) / 11);
                    image = image.png({
                        compressionLevel: Math.max(0, Math.min(9, compressionLevel)),
                        progressive: options.progressive ?? false,
                        palette: options.quality < 80 // Use palette for lower quality
                    });
                    break;
                case 'webp':
                    image = image.webp({
                        quality: options.quality,
                        lossless: options.quality >= 95,
                        nearLossless: options.quality >= 90 && options.quality < 95,
                        smartSubsample: true
                    });
                    break;
                default:
                    throw new Error(`Unsupported format: ${options.format}`);
            }
            const compressedBuffer = await image.toBuffer();
            const compressedSize = compressedBuffer.length;
            const compressionRatio = originalSize / compressedSize;
            logger.debug('Image compressed', {
                originalSize,
                compressedSize,
                compressionRatio: Math.round(compressionRatio * 100) / 100,
                format: options.format,
                quality: options.quality
            });
            return {
                buffer: compressedBuffer,
                originalSize,
                compressedSize,
                compressionRatio,
                format: options.format
            };
        }
        catch (error) {
            logger.error('Image compression failed', error);
            throw new Error(`Compression failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Auto-optimize image based on content analysis
     */
    static async autoOptimize(inputBuffer, targetSizeKB, maxQuality = 90) {
        const image = sharp(inputBuffer);
        const metadata = await image.metadata();
        // Determine best format based on image characteristics
        let format = 'jpeg';
        if (metadata.channels === 4 || metadata.hasAlpha) {
            // Has transparency, prefer PNG or WebP
            format = 'webp';
        }
        else if (metadata.density && metadata.density > 150) {
            // High DPI image, prefer WebP for better compression
            format = 'webp';
        }
        // Start with high quality and reduce if needed
        let quality = maxQuality;
        let result;
        do {
            result = await this.compress(inputBuffer, { quality, format });
            // If target size is specified and we're over it, reduce quality
            if (targetSizeKB && result.compressedSize > targetSizeKB * 1024) {
                quality -= 10;
            }
            else {
                break;
            }
        } while (quality > 20);
        return result;
    }
    /**
     * Adaptive compression based on content type
     */
    static async adaptiveCompress(inputBuffer, contentType, quality = 80) {
        let options;
        switch (contentType) {
            case 'screenshot':
                // Screenshots often have sharp edges and text
                options = {
                    quality: Math.max(quality, 70), // Maintain readability
                    format: 'png', // Better for sharp edges
                    progressive: false
                };
                break;
            case 'photo':
                // Photos benefit from JPEG compression
                options = {
                    quality,
                    format: 'jpeg',
                    progressive: true,
                    mozjpeg: true
                };
                break;
            case 'diagram':
                // Diagrams have few colors, PNG is efficient
                options = {
                    quality: Math.max(quality, 85),
                    format: 'png',
                    progressive: false
                };
                break;
            case 'text':
                // Text needs high quality for readability
                options = {
                    quality: Math.max(quality, 90),
                    format: 'png',
                    progressive: false
                };
                break;
            default:
                options = { quality, format: 'jpeg' };
        }
        return this.compress(inputBuffer, options);
    }
    /**
     * Resize image while maintaining aspect ratio
     */
    static async resizeAndCompress(inputBuffer, maxWidth, maxHeight, options) {
        const image = sharp(inputBuffer);
        const metadata = await image.metadata();
        if (!metadata.width || !metadata.height) {
            throw new Error('Unable to determine image dimensions');
        }
        // Calculate new dimensions maintaining aspect ratio
        const aspectRatio = metadata.width / metadata.height;
        let newWidth = metadata.width;
        let newHeight = metadata.height;
        if (newWidth > maxWidth) {
            newWidth = maxWidth;
            newHeight = Math.round(newWidth / aspectRatio);
        }
        if (newHeight > maxHeight) {
            newHeight = maxHeight;
            newWidth = Math.round(newHeight * aspectRatio);
        }
        // Resize if needed
        let processedImage = image;
        if (newWidth !== metadata.width || newHeight !== metadata.height) {
            processedImage = image.resize(newWidth, newHeight, {
                kernel: sharp.kernel.lanczos3,
                withoutEnlargement: true
            });
        }
        const resizedBuffer = await processedImage.toBuffer();
        return this.compress(resizedBuffer, options);
    }
}
//# sourceMappingURL=compression.js.map