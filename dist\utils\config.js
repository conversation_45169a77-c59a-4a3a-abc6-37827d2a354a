import { config } from 'dotenv';
import { ServerConfigSchema } from '../types/index.js';
// Load environment variables
config();
/**
 * Load and validate server configuration from environment variables
 */
export function loadConfig() {
    const rawConfig = {
        port: parseInt(process.env.PORT || '3000', 10),
        host: process.env.HOST || 'localhost',
        nodeEnv: process.env.NODE_ENV || 'development',
        streaming: {
            defaultFps: parseFloat(process.env.DEFAULT_FPS || '1'),
            maxFps: parseFloat(process.env.MAX_FPS || '10'),
            defaultQuality: parseInt(process.env.DEFAULT_QUALITY || '80', 10),
            maxConcurrentStreams: parseInt(process.env.MAX_CONCURRENT_STREAMS || '5', 10)
        },
        security: {
            enableAuth: process.env.ENABLE_AUTH === 'true',
            rateLimitPerMinute: parseInt(process.env.RATE_LIMIT_PER_MINUTE || '100', 10),
            allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['*']
        },
        performance: {
            enableDiffDetection: process.env.ENABLE_DIFF_DETECTION !== 'false',
            enableHardwareAcceleration: process.env.ENABLE_HARDWARE_ACCELERATION === 'true',
            memoryLimitMb: parseInt(process.env.MEMORY_LIMIT_MB || '512', 10),
            cacheSizeMb: parseInt(process.env.CACHE_SIZE_MB || '100', 10)
        }
    };
    // Validate configuration
    const result = ServerConfigSchema.safeParse(rawConfig);
    if (!result.success) {
        console.error('Configuration validation failed:', result.error.format());
        throw new Error('Invalid server configuration');
    }
    return result.data;
}
/**
 * Get current configuration
 */
export const serverConfig = loadConfig();
//# sourceMappingURL=config.js.map