import { StartStreamInputSchema } from '../types/index.js';
import { logger } from '../utils/logger.js';
/**
 * Start screen stream tool handler
 */
export async function startStreamTool(streamManager, args) {
    try {
        // Validate input arguments
        const validatedArgs = StartStreamInputSchema.parse(args);
        logger.info('Starting new screen stream', validatedArgs);
        // Start the stream
        const streamId = await streamManager.startStream(validatedArgs);
        // Get initial stream status
        const status = streamManager.getStreamStatus(streamId);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        streamId,
                        message: 'Screen stream started successfully',
                        status,
                        instructions: {
                            'stop_stream': `Use stop_screen_stream tool with streamId: ${streamId}`,
                            'configure_stream': `Use configure_stream tool to modify settings`,
                            'get_status': `Use get_stream_status tool to monitor performance`
                        }
                    }, null, 2)
                }
            ]
        };
    }
    catch (error) {
        logger.error('Failed to start stream', error);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                        code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
                    }, null, 2)
                }
            ],
            isError: true
        };
    }
}
//# sourceMappingURL=startStream.js.map