import { ConfigureStreamInputSchema } from '../types/index.js';
import { logger } from '../utils/logger.js';
/**
 * Configure stream tool handler
 */
export async function configureStreamTool(streamManager, args) {
    try {
        // Validate input arguments
        const validatedArgs = ConfigureStreamInputSchema.parse(args);
        const { streamId, ...configChanges } = validatedArgs;
        logger.info(`Configuring stream: ${streamId}`, configChanges);
        // Configure the stream
        const success = await streamManager.configureStream(streamId, configChanges);
        // Get updated status
        const updatedStatus = streamManager.getStreamStatus(streamId);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success,
                        streamId,
                        message: 'Stream configuration updated successfully',
                        appliedChanges: configChanges,
                        currentStatus: updatedStatus
                    }, null, 2)
                }
            ]
        };
    }
    catch (error) {
        logger.error('Failed to configure stream', error);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                        code: error instanceof Error && 'code' in error ? error.code : 'UNKNOWN_ERROR'
                    }, null, 2)
                }
            ],
            isError: true
        };
    }
}
//# sourceMappingURL=configureStream.js.map