{"version": 3, "file": "memoryManager.js", "sourceRoot": "", "sources": ["../../src/utils/memoryManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAE3C;;GAEG;AACH,MAAM,OAAO,aAAa;IAChB,mBAAmB,GAA0B,IAAI,CAAC;IAClD,gBAAgB,CAAS;IACzB,WAAW,CAAS;IACpB,UAAU,GAAW,CAAC,CAAC;IACvB,aAAa,GAAa,EAAE,CAAC;IAErC;QACE,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;QAC7E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,6BAA6B;QAC7E,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,GAAG,EAAE;YAC1C,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,yBAAyB;QAEpC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACvC,aAAa,EAAE,YAAY,CAAC,WAAW,CAAC,aAAa;YACrD,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;SAC1D,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACnC,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACnC,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;QAEzB,oCAAoC;QACpC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,iCAAiC;QAC/D,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE;YAC3B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;YAC9C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;YAChD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;YAC9C,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;YACpC,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,aAAa;SAChD,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;gBAC7C,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,aAAa;aAChD,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,mCAAmC;aAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC/D,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBAC3C,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;gBAC7C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;aACxD,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,eAAe,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAE9C,iEAAiE;QACjE,IAAI,eAAe,GAAG,MAAM,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpE,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;YAChD,MAAM,CAAC,EAAE,EAAE,CAAC;YACZ,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;YAC/C,MAAM,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC;YAEjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;gBACxC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;gBAC5C,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;aAC3C,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAY,EAAE,YAAoB;QAC1D,uEAAuE;QACvE,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;YAC7B,IAAI;YACJ,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC;YACtD,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,aAAa;YAC/C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc;QAYZ,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC;QAErE,kBAAkB;QAClB,IAAI,KAAK,GAA2C,QAAQ,CAAC;QAC7D,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;gBAAE,KAAK,GAAG,YAAY,CAAC;iBACjD,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;gBAAE,KAAK,GAAG,YAAY,CAAC;QAC7D,CAAC;QAED,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,KAAK,EAAE,IAAI,CAAC,gBAAgB;YAC5B,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC9C,KAAK;aACN;YACD,EAAE,EAAE;gBACF,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU;aAC9C;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACpC,OAAO,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACpC,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YAChC,eAAe,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QAC3F,CAAC;aAAM,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YACvC,eAAe,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,YAAY,EAAE,CAAC;YACvC,eAAe,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YACf,eAAe,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;CACF;AAED,iCAAiC;AACjC,MAAM,CAAC,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC"}