{"version": 3, "file": "ClassVisitor.d.ts", "sourceRoot": "", "sources": ["../../src/referencer/ClassVisitor.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAIzD,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE/C,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEpC,cAAM,YAAa,SAAQ,OAAO;;gBAK9B,UAAU,EAAE,UAAU,EACtB,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe;IAO5D,MAAM,CAAC,KAAK,CACV,UAAU,EAAE,UAAU,EACtB,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,GACzD,IAAI;IAKP,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAanD,SAAS,CAAC,UAAU,CAClB,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,GACzD,IAAI;IAgCP,SAAS,CAAC,uBAAuB,CAC/B,IAAI,EACA,QAAQ,CAAC,gBAAgB,GACzB,QAAQ,CAAC,kBAAkB,GAC3B,QAAQ,CAAC,0BAA0B,GACnC,QAAQ,CAAC,4BAA4B,GACxC,IAAI;IAWP,SAAS,CAAC,oCAAoC,CAC5C,IAAI,EAAE,QAAQ,CAAC,SAAS,GACvB,IAAI;IAaP,SAAS,CAAC,mBAAmB,CAC3B,IAAI,EAAE,QAAQ,CAAC,kBAAkB,EACjC,UAAU,EAAE,QAAQ,CAAC,gBAAgB,GACpC,IAAI;IA2GP,SAAS,CAAC,iBAAiB,CACzB,IAAI,EACA,QAAQ,CAAC,gBAAgB,GACzB,QAAQ,CAAC,kBAAkB,GAC3B,QAAQ,CAAC,0BAA0B,GACnC,QAAQ,CAAC,0BAA0B,GACnC,QAAQ,CAAC,4BAA4B,GACxC,IAAI;IA4BP,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAc5D,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAWjE,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAIjE,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,GAAG,IAAI;IAMnD,SAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,kBAAkB,GAAG,IAAI;IAIrE,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAIjE,SAAS,CAAC,0BAA0B,CAClC,IAAI,EAAE,QAAQ,CAAC,0BAA0B,GACxC,IAAI;IAIP,SAAS,CAAC,4BAA4B,CACpC,IAAI,EAAE,QAAQ,CAAC,4BAA4B,GAC1C,IAAI;IAIP,SAAS,CAAC,0BAA0B,CAClC,IAAI,EAAE,QAAQ,CAAC,0BAA0B,GACxC,IAAI;IAIP,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI;IAIrD,SAAS,CAAC,iBAAiB,IAAI,IAAI;IAInC,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAIjE,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI;CAOxD;AAsCD,OAAO,EAAE,YAAY,EAAE,CAAC"}